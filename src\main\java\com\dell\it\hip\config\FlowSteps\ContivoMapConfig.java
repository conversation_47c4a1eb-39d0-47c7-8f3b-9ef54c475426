package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContivoMapConfig extends FlowStepConfig {
    private String mapIdentifier;
    private String mapName;
    private String mapClass;
    private String contivoVersion;
    private CrossReferenceData crossReferenceData;
    private String redisMapDataKey;
    private String mapIdentifierVersion;

    // Getters and Setters
    // toString, equals, hashCode (optional)
}
