package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class MappingTransformerFlowStepConfigTest {

    @Test
    public void testSuccessfulDeserialization() throws Exception {
        // JSON with complete structure matching mapping-config.json
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"emfp-request-850\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"ContivoMapForRequest850\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].version\": \"1.0\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": true,\n"
                + "  \"docTypeConfigs[1].transformerRef\": \"emfp-request-850\",\n"
                + "  \"docTypeConfigs[1].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[1].ruleRefs[0].name\": \"ContivoMapForRequest850\",\n"
                + "  \"docTypeConfigs[1].ruleRefs[0].version\": \"1.0\",\n"
                + "  \"docTypeConfigs[1].isDbBacked\": true\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getDocTypeConfigs());
        assertEquals(2, config.getDocTypeConfigs().size());
        
        // Check first docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig0 = config.getDocTypeConfigs().get(0);
        assertNotNull(docTypeConfig0);
        assertEquals("emfp-request-850", docTypeConfig0.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM, docTypeConfig0.getBehavior());
        assertTrue(docTypeConfig0.isDbBacked());
        
        // Check first docTypeConfig's ruleRefs
        assertNotNull(docTypeConfig0.getRuleRefs());
        assertEquals(1, docTypeConfig0.getRuleRefs().size());
        RuleRef ruleRef0 = docTypeConfig0.getRuleRefs().get(0);
        assertNotNull(ruleRef0);
        assertEquals("ContivoMapForRequest850", ruleRef0.getRuleName());
        assertEquals("1.0", ruleRef0.getRuleVersion());
        
        // Check second docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig1 = config.getDocTypeConfigs().get(1);
        assertNotNull(docTypeConfig1);
        assertEquals("emfp-request-850", docTypeConfig1.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM, docTypeConfig1.getBehavior());
        assertTrue(docTypeConfig1.isDbBacked());
        
        // Check second docTypeConfig's ruleRefs
        assertNotNull(docTypeConfig1.getRuleRefs());
        assertEquals(1, docTypeConfig1.getRuleRefs().size());
        RuleRef ruleRef1 = docTypeConfig1.getRuleRefs().get(0);
        assertNotNull(ruleRef1);
        assertEquals("ContivoMapForRequest850", ruleRef1.getRuleName());
        assertEquals("1.0", ruleRef1.getRuleVersion());
        
        System.out.println("✅ Successfully deserialized complete JSON structure!");
    }

    @Test
    public void testSingleDocTypeConfigDeserialization() throws Exception {
        // JSON with single docTypeConfig
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"single-transformer\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"SKIP\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"SingleRule\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].version\": \"2.0\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());
        
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig = config.getDocTypeConfigs().get(0);
        assertEquals("single-transformer", docTypeConfig.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.SKIP, docTypeConfig.getBehavior());
        assertFalse(docTypeConfig.isDbBacked());
        
        assertEquals(1, docTypeConfig.getRuleRefs().size());
        RuleRef ruleRef = docTypeConfig.getRuleRefs().get(0);
        assertEquals("SingleRule", ruleRef.getRuleName());
        assertEquals("2.0", ruleRef.getRuleVersion());
    }

    @Test
    public void testMultipleRuleRefsDeserialization() throws Exception {
        // JSON with multiple ruleRefs for a single docTypeConfig
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"multi-rule-transformer\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"TERMINATE\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"FirstRule\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].version\": \"1.0\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[1].name\": \"SecondRule\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[1].version\": \"2.0\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[2].name\": \"ThirdRule\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[2].version\": \"3.0\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": true\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());
        
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig = config.getDocTypeConfigs().get(0);
        assertEquals("multi-rule-transformer", docTypeConfig.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TERMINATE, docTypeConfig.getBehavior());
        assertTrue(docTypeConfig.isDbBacked());
        
        // Check all three ruleRefs
        assertEquals(3, docTypeConfig.getRuleRefs().size());
        
        RuleRef ruleRef0 = docTypeConfig.getRuleRefs().get(0);
        assertEquals("FirstRule", ruleRef0.getRuleName());
        assertEquals("1.0", ruleRef0.getRuleVersion());
        
        RuleRef ruleRef1 = docTypeConfig.getRuleRefs().get(1);
        assertEquals("SecondRule", ruleRef1.getRuleName());
        assertEquals("2.0", ruleRef1.getRuleVersion());
        
        RuleRef ruleRef2 = docTypeConfig.getRuleRefs().get(2);
        assertEquals("ThirdRule", ruleRef2.getRuleName());
        assertEquals("3.0", ruleRef2.getRuleVersion());
    }

    @Test
    public void testEmptyJsonDeserialization() throws Exception {
        // Empty JSON object
        String json = "{}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        assertNotNull(config);
        assertNotNull(config.getDocTypeConfigs());
        assertTrue(config.getDocTypeConfigs().isEmpty());
        assertNull(config.getDefaultConfig());
    }

    @Test
    public void testPartialDataDeserialization() throws Exception {
        // JSON with missing some fields
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"partial-transformer\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"PartialRule\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        
        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());
        
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig = config.getDocTypeConfigs().get(0);
        assertEquals("partial-transformer", docTypeConfig.getTransformerRef());
        // Default behavior should be TRANSFORM
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM, docTypeConfig.getBehavior());
        // Default isDbBacked should be false
        assertFalse(docTypeConfig.isDbBacked());
        
        // RuleRef should have name but no version
        assertEquals(1, docTypeConfig.getRuleRefs().size());
        RuleRef ruleRef = docTypeConfig.getRuleRefs().get(0);
        assertEquals("PartialRule", ruleRef.getRuleName());
        assertNull(ruleRef.getRuleVersion());
    }

    @Test
    public void testInvalidBehaviorHandling() throws Exception {
        // JSON with invalid behavior value - should throw exception
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"test-transformer\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"INVALID_BEHAVIOR\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": true\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // This should throw an exception due to invalid enum value
        assertThrows(Exception.class, () -> {
            objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);
        });
    }

    @Test
    public void testNonSequentialIndicesDeserialization() throws Exception {
        // JSON with non-sequential indices (0, 2, 5) - should handle gaps
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"transformer-0\",\n"
                + "  \"docTypeConfigs[0].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[2].transformerRef\": \"transformer-2\",\n"
                + "  \"docTypeConfigs[2].behavior\": \"SKIP\",\n"
                + "  \"docTypeConfigs[5].transformerRef\": \"transformer-5\",\n"
                + "  \"docTypeConfigs[5].behavior\": \"TERMINATE\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);

        assertNotNull(config);
        // Should create 6 entries (0-5) with nulls for missing indices
        assertEquals(6, config.getDocTypeConfigs().size());

        // Check index 0
        assertNotNull(config.getDocTypeConfigs().get(0));
        assertEquals("transformer-0", config.getDocTypeConfigs().get(0).getTransformerRef());

        // Check index 1 (should be null/empty)
        assertNull(config.getDocTypeConfigs().get(1));

        // Check index 2
        assertNotNull(config.getDocTypeConfigs().get(2));
        assertEquals("transformer-2", config.getDocTypeConfigs().get(2).getTransformerRef());

        // Check indices 3, 4 (should be null/empty)
        assertNull(config.getDocTypeConfigs().get(3));
        assertNull(config.getDocTypeConfigs().get(4));

        // Check index 5
        assertNotNull(config.getDocTypeConfigs().get(5));
        assertEquals("transformer-5", config.getDocTypeConfigs().get(5).getTransformerRef());
    }

    @Test
    public void testBooleanTypeValidation() throws Exception {
        // Test various boolean representations
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"bool-test\",\n"
                + "  \"docTypeConfigs[0].isDbBacked\": \"true\"\n"  // String "true" should work
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);

        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());
        assertTrue(config.getDocTypeConfigs().get(0).isDbBacked());
    }

    @Test
    public void testMalformedJsonHandling() throws Exception {
        // Test with malformed field names that don't match pattern
        String json = "{\n"
                + "  \"docTypeConfigs[0].transformerRef\": \"valid-transformer\",\n"
                + "  \"invalidField\": \"should-be-ignored\",\n"
                + "  \"docTypeConfigs.invalid\": \"should-be-ignored\",\n"
                + "  \"docTypeConfigs[abc].transformerRef\": \"should-be-ignored\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);

        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());
        assertEquals("valid-transformer", config.getDocTypeConfigs().get(0).getTransformerRef());
    }

    @Test
    public void testAllMappingBehaviorValues() throws Exception {
        // Test all valid MappingBehavior enum values
        String json = "{\n"
                + "  \"docTypeConfigs[0].behavior\": \"TRANSFORM\",\n"
                + "  \"docTypeConfigs[1].behavior\": \"SKIP\",\n"
                + "  \"docTypeConfigs[2].behavior\": \"TERMINATE\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);

        assertNotNull(config);
        assertEquals(3, config.getDocTypeConfigs().size());

        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM,
                    config.getDocTypeConfigs().get(0).getBehavior());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.SKIP,
                    config.getDocTypeConfigs().get(1).getBehavior());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TERMINATE,
                    config.getDocTypeConfigs().get(2).getBehavior());
    }

    @Test
    public void testRuleRefWithMissingFields() throws Exception {
        // Test ruleRef with only name or only version
        String json = "{\n"
                + "  \"docTypeConfigs[0].ruleRefs[0].name\": \"OnlyNameRule\",\n"
                + "  \"docTypeConfigs[0].ruleRefs[1].version\": \"2.0\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        MappingTransformerFlowStepConfig config = objectMapper.readValue(json, MappingTransformerFlowStepConfig.class);

        assertNotNull(config);
        assertEquals(1, config.getDocTypeConfigs().size());

        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig = config.getDocTypeConfigs().get(0);
        assertEquals(2, docTypeConfig.getRuleRefs().size());

        // First ruleRef has only name
        RuleRef ruleRef0 = docTypeConfig.getRuleRefs().get(0);
        assertEquals("OnlyNameRule", ruleRef0.getRuleName());
        assertNull(ruleRef0.getRuleVersion());

        // Second ruleRef has only version
        RuleRef ruleRef1 = docTypeConfig.getRuleRefs().get(1);
        assertNull(ruleRef1.getRuleName());
        assertEquals("2.0", ruleRef1.getRuleVersion());
    }

    @Test
    public void testIntegrationWithActualMappingConfigJson() throws Exception {
        // Load the actual mapping-config.json file from test resources
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("mapping-config.json");
        assertNotNull(inputStream, "mapping-config.json file should exist in test resources");

        // Read the JSON content
        String jsonContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        assertNotNull(jsonContent);
        assertFalse(jsonContent.trim().isEmpty());

        ObjectMapper objectMapper = new ObjectMapper();

        // Test deserialization of the actual file
        MappingTransformerFlowStepConfig config = objectMapper.readValue(jsonContent, MappingTransformerFlowStepConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getDocTypeConfigs());
        assertEquals(2, config.getDocTypeConfigs().size(), "Should have 2 docTypeConfigs as per mapping-config.json");

        // Verify first docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig0 = config.getDocTypeConfigs().get(0);
        assertNotNull(docTypeConfig0);
        assertEquals("emfp-request-850", docTypeConfig0.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM, docTypeConfig0.getBehavior());
        assertTrue(docTypeConfig0.isDbBacked());

        // Verify first docTypeConfig's ruleRefs
        assertNotNull(docTypeConfig0.getRuleRefs());
        assertEquals(1, docTypeConfig0.getRuleRefs().size());
        RuleRef ruleRef0 = docTypeConfig0.getRuleRefs().get(0);
        assertNotNull(ruleRef0);
        assertEquals("ContivoMapForRequest850", ruleRef0.getRuleName());
        assertEquals("1.0", ruleRef0.getRuleVersion());

        // Verify second docTypeConfig
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig docTypeConfig1 = config.getDocTypeConfigs().get(1);
        assertNotNull(docTypeConfig1);
        assertEquals("emfp-request-850", docTypeConfig1.getTransformerRef());
        assertEquals(MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM, docTypeConfig1.getBehavior());
        assertTrue(docTypeConfig1.isDbBacked());

        // Verify second docTypeConfig's ruleRefs
        assertNotNull(docTypeConfig1.getRuleRefs());
        assertEquals(1, docTypeConfig1.getRuleRefs().size());
        RuleRef ruleRef1 = docTypeConfig1.getRuleRefs().get(0);
        assertNotNull(ruleRef1);
        assertEquals("ContivoMapForRequest850", ruleRef1.getRuleName());
        assertEquals("1.0", ruleRef1.getRuleVersion());

        System.out.println("✅ Successfully deserialized actual mapping-config.json file!");
        System.out.println("Found " + config.getDocTypeConfigs().size() + " docTypeConfigs");
        System.out.println("First docTypeConfig transformer: " + docTypeConfig0.getTransformerRef());
        System.out.println("First docTypeConfig ruleRef: " + ruleRef0.getRuleName() + " v" + ruleRef0.getRuleVersion());
    }
}
