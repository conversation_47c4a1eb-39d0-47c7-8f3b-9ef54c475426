package com.dell.it.hip.exception;

/**
 * Exception thrown when attempting to register an integration that already exists.
 * This exception is mapped to HTTP 409 Conflict status code.
 */
public class IntegrationDuplicateException extends RuntimeException {
    
    private final String serviceManagerName;
    private final String integrationName;
    private final String version;
    
    public IntegrationDuplicateException(String message) {
        super(message);
        this.serviceManagerName = null;
        this.integrationName = null;
        this.version = null;
    }
    
    public IntegrationDuplicateException(String message, Throwable cause) {
        super(message, cause);
        this.serviceManagerName = null;
        this.integrationName = null;
        this.version = null;
    }
    
    public IntegrationDuplicateException(String serviceManagerName, String integrationName, String version) {
        super(String.format("Integration with service manager '%s', integration '%s', and version '%s' already exists", 
                          serviceManagerName, integrationName, version));
        this.serviceManagerName = serviceManagerName;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationDuplicateException(String serviceManagerName, String integrationName, String version, String message) {
        super(String.format("Integration with service manager '%s', integration '%s', and version '%s' already exists: %s", 
                          serviceManagerName, integrationName, version, message));
        this.serviceManagerName = serviceManagerName;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public IntegrationDuplicateException(String serviceManagerName, String integrationName, String version, Throwable cause) {
        super(String.format("Integration with service manager '%s', integration '%s', and version '%s' already exists: %s", 
                          serviceManagerName, integrationName, version, cause.getMessage()), cause);
        this.serviceManagerName = serviceManagerName;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public String getServiceManagerName() {
        return serviceManagerName;
    }
    
    public String getIntegrationName() {
        return integrationName;
    }
    
    public String getVersion() {
        return version;
    }
}
