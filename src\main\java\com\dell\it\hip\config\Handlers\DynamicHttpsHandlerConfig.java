package com.dell.it.hip.config.Handlers;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicHttpsHandlerConfig extends HandlerConfig {

    @JsonProperty("https.producer.endpoint.url")
    private String endpointUrl;

    @JsonProperty("https.producer.http.method")
    private String httpMethod; // GET, POST, PUT, etc.

    @JsonProperty("https.producer.headers")
    private Map<String, String> headers; // default headers

    @JsonProperty("https.producer.api.key.header")
    private String apiKeyHeader; // e.g. "x-api-key"

    @JsonProperty("https.producer.api.key.value")
    private String apiKeyValue;

    @JsonProperty("https.producer.connect.timeout.ms")
    private Long connectTimeoutMs;

    @JsonProperty("https.producer.read.timeout.ms")
    private Long readTimeoutMs;

    @JsonProperty("https.producer.max.in.memory.size")
    private Integer maxInMemorySize;

    @JsonProperty("https.producer.retry.attempts")
    private Integer retryAttempts;

    @JsonProperty("https.producer.retry.backoff.ms")
    private Long retryBackoffMs;

    @JsonProperty("https.producer.compressed")
    private Boolean compressed; // gzip or not

    // OAuth2
    @JsonProperty("https.producer.oauth.enabled")
    private Boolean oauthEnabled;

    @JsonProperty("https.producer.oauth.token.url")
    private String oauthTokenUrl;

    @JsonProperty("https.producer.oauth.client.id")
    private String oauthClientId;

    @JsonProperty("https.producer.oauth.client.secret")
    private String oauthClientSecret;

    @JsonProperty("https.producer.oauth.scope")
    private String oauthScope;

    @JsonProperty("https.producer.oauth.audience")
    private String oauthAudience;

    @JsonProperty("https.producer.oauth.additional.params")
    private Map<String, String> oauthAdditionalParams;

    public Boolean isOauthEnabled() { return oauthEnabled; }
}