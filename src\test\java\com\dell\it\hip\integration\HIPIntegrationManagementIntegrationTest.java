package com.dell.it.hip.integration;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.dell.it.hip.controller.HIPIntegrationManagementController;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationDuplicateException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.hamcrest.Matchers.containsString;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for HIP Integration Management functionality.
 * Tests the complete flow including duplicate validation and BigDecimal version handling.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class HIPIntegrationManagementIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @Autowired
    private HIPIntegrationManagementController controller;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .apply(springSecurity())
                .build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_Success() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("test-integration", "1.0");

        // Act & Assert
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().string("HIPIntegration registered"));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_DuplicateValidation() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("duplicate-test", "1.0");

        // Act - First registration should succeed
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // Act - Second registration should fail with 409 Conflict
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.errorCode").value("INTEGRATION_ALREADY_EXISTS"))
                .andExpect(jsonPath("$.message").value(containsString("already exists")))
                .andExpect(jsonPath("$.message").value(containsString("duplicate-test")))
                .andExpect(jsonPath("$.message").value(containsString("1.0")));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_DifferentVersionsAllowed() throws Exception {
        // Arrange
        HIPIntegrationRequest request1 = createTestRequest("version-test", "1.0");
        HIPIntegrationRequest request2 = createTestRequest("version-test", "2.0");

        // Act & Assert - Both should succeed
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request1)))
                .andExpect(status().isOk());

        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request2)))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_SemanticVersionHandling() throws Exception {
        // Test that semantic versions are handled correctly
        String[] semanticVersions = {"1.0.0", "1.2.3", "2.1.0"};

        for (String version : semanticVersions) {
            HIPIntegrationRequest request = createTestRequest("semantic-test-" + version.replace(".", ""), version);

            mockMvc.perform(post("/hip/management/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().string("HIPIntegration registered"));
        }
    }

    @Test
    void testRegisterIntegration_UnauthorizedAccess() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("unauthorized-test", "1.0");

        // Act & Assert - Should fail without proper role
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = "USER") // Wrong role
    void testRegisterIntegration_InsufficientRole() throws Exception {
        // Arrange
        HIPIntegrationRequest request = createTestRequest("role-test", "1.0");

        // Act & Assert - Should fail with insufficient role
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    void testHIPIntegrationRequestEntity_VersionConversion() {
        // Test the BigDecimal version conversion in isolation
        HIPIntegrationRequestEntity entity = new HIPIntegrationRequestEntity();

        // Test simple decimal versions
        entity.setVersion("1.0");
        assertEquals("1.0", entity.getVersion());

        // Test semantic versions
        entity.setVersion("1.2.3");
        assertEquals("1.0203", entity.getVersion()); // Converted to decimal format

        // Test null handling
        entity.setVersion(null);
        assertNull(entity.getVersion());

        // Test BigDecimal access
        entity.setVersion("2.5");
        assertNotNull(entity.getVersionBigDecimal());
        assertEquals(0, entity.getVersionBigDecimal().compareTo(new java.math.BigDecimal("2.5")));
    }

    @Test
    void testIntegrationDuplicateException_Properties() {
        // Test the exception properties
        String serviceManager = "test-service";
        String integration = "test-integration";
        String version = "1.0";

        IntegrationDuplicateException exception = new IntegrationDuplicateException(
                serviceManager, integration, version);

        assertEquals(serviceManager, exception.getServiceManagerName());
        assertEquals(integration, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertTrue(exception.getMessage().contains(serviceManager));
        assertTrue(exception.getMessage().contains(integration));
        assertTrue(exception.getMessage().contains(version));
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_InvalidRequestData() throws Exception {
        // Test with invalid JSON
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json}"))
                .andExpect(status().isBadRequest());

        // Test with missing required fields
        mockMvc.perform(post("/hip/management/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(roles = "ADMIN")
    void testRegisterIntegration_VersionFormatVariations() throws Exception {
        // Test various version formats that should be accepted
        String[] validVersions = {"1", "1.0", "1.0.0", "2.1.3", "10.5"};

        for (String version : validVersions) {
            HIPIntegrationRequest request = createTestRequest("version-format-" + version.replace(".", ""), version);

            mockMvc.perform(post("/hip/management/register")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isOk())
                    .andExpect(content().string("HIPIntegration registered"));
        }
    }

    private HIPIntegrationRequest createTestRequest(String integrationName, String version) {
        HIPIntegrationRequest request = new HIPIntegrationRequest();
        request.setHipIntegrationName(integrationName);
        request.setVersion(version);
        request.setBusinessFlowName("test-flow");
        // Note: HIPIntegrationRequest may not have setDescription method
        return request;
    }
}
