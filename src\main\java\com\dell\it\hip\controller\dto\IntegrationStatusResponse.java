package com.dell.it.hip.controller.dto;

import java.util.List;

import com.dell.it.hip.config.IntegrationStatus;

/**
 * Response DTO for the /{name}/status endpoint that returns status information
 * for all versions of a specified integration name.
 * 
 * Contains a list of integration version status information, where each entry
 * includes the integration name, version, and current status.
 */
public class IntegrationStatusResponse {
    
    /**
     * List of integration version status information
     */
    private List<IntegrationVersionStatus> integrations;
    
    /**
     * Default constructor
     */
    public IntegrationStatusResponse() {
    }
    
    /**
     * Constructor with integrations list
     * 
     * @param integrations List of integration version status information
     */
    public IntegrationStatusResponse(List<IntegrationVersionStatus> integrations) {
        this.integrations = integrations;
    }
    
    /**
     * Get the list of integration version status information
     * 
     * @return List of IntegrationVersionStatus objects
     */
    public List<IntegrationVersionStatus> getIntegrations() {
        return integrations;
    }
    
    /**
     * Set the list of integration version status information
     * 
     * @param integrations List of IntegrationVersionStatus objects
     */
    public void setIntegrations(List<IntegrationVersionStatus> integrations) {
        this.integrations = integrations;
    }
    
    @Override
    public String toString() {
        return "IntegrationStatusResponse{" +
                "integrations=" + (integrations != null ? integrations.size() + " items" : "null") +
                '}';
    }
    
    /**
     * Inner class representing status information for a single integration version
     */
    public static class IntegrationVersionStatus {
        
        /**
         * Integration name
         */
        private String name;
        
        /**
         * Integration version
         */
        private String version;
        
        /**
         * Current integration status
         */
        private IntegrationStatus status;
        
        /**
         * Default constructor
         */
        public IntegrationVersionStatus() {
        }
        
        /**
         * Constructor with all fields
         * 
         * @param name Integration name
         * @param version Integration version
         * @param status Integration status
         */
        public IntegrationVersionStatus(String name, String version, IntegrationStatus status) {
            this.name = name;
            this.version = version;
            this.status = status;
        }
        
        /**
         * Get the integration name
         * 
         * @return Integration name
         */
        public String getName() {
            return name;
        }
        
        /**
         * Set the integration name
         * 
         * @param name Integration name
         */
        public void setName(String name) {
            this.name = name;
        }
        
        /**
         * Get the integration version
         * 
         * @return Integration version
         */
        public String getVersion() {
            return version;
        }
        
        /**
         * Set the integration version
         * 
         * @param version Integration version
         */
        public void setVersion(String version) {
            this.version = version;
        }
        
        /**
         * Get the integration status
         * 
         * @return Integration status
         */
        public IntegrationStatus getStatus() {
            return status;
        }
        
        /**
         * Set the integration status
         * 
         * @param status Integration status
         */
        public void setStatus(IntegrationStatus status) {
            this.status = status;
        }
        
        @Override
        public String toString() {
            return "IntegrationVersionStatus{" +
                    "name='" + name + '\'' +
                    ", version='" + version + '\'' +
                    ", status=" + status +
                    '}';
        }
    }
}
