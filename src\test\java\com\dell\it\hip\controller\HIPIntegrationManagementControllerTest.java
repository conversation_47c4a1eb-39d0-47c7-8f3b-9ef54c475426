package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.IntegrationStatus;
import com.dell.it.hip.controller.dto.IntegrationStatusResponse;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for HIPIntegrationManagementController definition retrieval methods.
 * Tests both single definition retrieval and multi-version definition retrieval.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerTest {

    private static final String SERVICE_MANAGER_NAME = "test-service-manager";

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationDefinition testDefinitionV1;
    private HIPIntegrationDefinition testDefinitionV2;
    private HIPIntegrationDefinition testDefinitionV3;

    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV1;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV2;
    private HIPIntegrationOrchestrationService.HIPIntegrationInfo testInfoV3;

    @BeforeEach
    void setUp() {
        // Reset mocks to ensure clean state for each test
        reset(serviceManager, orchestrationService, hipIntegrationRuntimeService);

        // Set the service manager name using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", SERVICE_MANAGER_NAME);

        // Create test definitions for different versions of the same integration
        testDefinitionV1 = createTestDefinition("test-integration", "1.0");
        testDefinitionV2 = createTestDefinition("test-integration", "2.0");
        testDefinitionV3 = createTestDefinition("test-integration", "3.0");

        // Create test integration info objects with status
        testInfoV1 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "1.0", IntegrationStatus.RUNNING);
        testInfoV2 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "2.0", IntegrationStatus.PAUSED);
        testInfoV3 = new HIPIntegrationOrchestrationService.HIPIntegrationInfo("test-integration", "3.0", IntegrationStatus.RUNNING);
    }

    // === Tests for existing getDefinition method ===

    @Test
    void testGetDefinition_Success() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testDefinitionV1);

        // Act
        ResponseEntity<?> response = controller.getDefinition("test-integration", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof HIPIntegrationDefinition);
        HIPIntegrationDefinition returnedDef = (HIPIntegrationDefinition) response.getBody();
        assertEquals("test-integration", returnedDef.getHipIntegrationName());
        assertEquals("1.0", returnedDef.getVersion());

        verify(serviceManager).getIntegrationDefinition("test-integration", "1.0");
    }

    @Test
    void testGetDefinition_NotFound() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("non-existent", "1.0"))
                .thenReturn(null);

        // Act
        ResponseEntity<?> response = controller.getDefinition("non-existent", "1.0");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getIntegrationDefinition("non-existent", "1.0");
    }
    // === Tests for new /{name}/status endpoint ===

    @Test
    void testGetIntegrationStatus_Success_MultipleVersions() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0"))
                .thenReturn(IntegrationStatus.PAUSED);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0"))
                .thenReturn(IntegrationStatus.RUNNING);

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof IntegrationStatusResponse);

        IntegrationStatusResponse responseBody = response.getBody();
        List<IntegrationStatusResponse.IntegrationVersionStatus> integrations = responseBody.getIntegrations();
        assertNotNull(integrations);
        assertEquals(3, integrations.size());

        // Verify all integrations have the same name
        integrations.forEach(integration ->
                assertEquals("test-integration", integration.getName()));

        // Verify different versions and statuses are present
        assertTrue(integrations.stream().anyMatch(i -> "1.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.RUNNING));
        assertTrue(integrations.stream().anyMatch(i -> "2.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.PAUSED));
        assertTrue(integrations.stream().anyMatch(i -> "3.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.RUNNING));

        // Verify service interactions
        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0");

        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_Success_SingleVersion() {
        // Arrange
        List<HIPIntegrationDefinition> expectedDefinitions = Collections.singletonList(testDefinitionV1);

        when(serviceManager.getDefinitionsByName("single-version-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("single-version-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof IntegrationStatusResponse);

        IntegrationStatusResponse responseBody = response.getBody();
        List<IntegrationStatusResponse.IntegrationVersionStatus> integrations = responseBody.getIntegrations();
        assertNotNull(integrations);
        assertEquals(1, integrations.size());

        IntegrationStatusResponse.IntegrationVersionStatus integration = integrations.get(0);
        assertEquals("test-integration", integration.getName());
        assertEquals("1.0", integration.getVersion());
        assertEquals(IntegrationStatus.RUNNING, integration.getStatus());

        // Verify service interactions
        verify(serviceManager, times(1)).getDefinitionsByName("single-version-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");

        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_NotFound_EmptyList() {
        // Arrange
        when(serviceManager.getDefinitionsByName("non-existent-integration"))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("non-existent-integration");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("non-existent-integration");
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_WithNullName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(null))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus(null);

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName(null);
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_WithEmptyName() {
        // Arrange
        when(serviceManager.getDefinitionsByName(""))
                .thenReturn(Collections.emptyList());

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("");

        // Assert
        assertEquals(404, response.getStatusCode().value());
        verify(serviceManager).getDefinitionsByName("");
        verifyNoInteractions(hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_MixedStatusValues() {
        // Arrange - Test all possible status values
        HIPIntegrationDefinition defError = createTestDefinition("test-integration", "4.0");
        HIPIntegrationDefinition defUnregistered = createTestDefinition("test-integration", "5.0");

        List<HIPIntegrationDefinition> expectedDefinitions = Arrays.asList(
                testDefinitionV1, testDefinitionV2, testDefinitionV3, defError, defUnregistered);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0"))
                .thenReturn(IntegrationStatus.PAUSED);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0"))
                .thenReturn(IntegrationStatus.RUNNING);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "4.0"))
                .thenReturn(IntegrationStatus.ERROR);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "5.0"))
                .thenReturn(IntegrationStatus.UNREGISTERED);

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        IntegrationStatusResponse responseBody = response.getBody();
        assertNotNull(responseBody);

        List<IntegrationStatusResponse.IntegrationVersionStatus> integrations = responseBody.getIntegrations();
        assertEquals(5, integrations.size());

        // Verify all status values are correctly returned
        assertTrue(integrations.stream().anyMatch(i -> "1.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.RUNNING));
        assertTrue(integrations.stream().anyMatch(i -> "2.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.PAUSED));
        assertTrue(integrations.stream().anyMatch(i -> "3.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.RUNNING));
        assertTrue(integrations.stream().anyMatch(i -> "4.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.ERROR));
        assertTrue(integrations.stream().anyMatch(i -> "5.0".equals(i.getVersion()) && i.getStatus() == IntegrationStatus.UNREGISTERED));

        // Verify service interactions
        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "2.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "3.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "4.0");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "5.0");

        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }

    @Test
    void testGetIntegrationStatus_StatusNotFoundDefaultsToUnregistered() {
        // Arrange - Definition exists but no status info available in Redis
        List<HIPIntegrationDefinition> expectedDefinitions = Collections.singletonList(testDefinitionV1);

        when(serviceManager.getDefinitionsByName("test-integration"))
                .thenReturn(expectedDefinitions);
        when(hipIntegrationRuntimeService.getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0"))
                .thenReturn(IntegrationStatus.UNREGISTERED); // Default when Redis key doesn't exist

        // Act
        ResponseEntity<IntegrationStatusResponse> response = controller.getIntegrationStatus("test-integration");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        IntegrationStatusResponse responseBody = response.getBody();
        assertNotNull(responseBody);

        List<IntegrationStatusResponse.IntegrationVersionStatus> integrations = responseBody.getIntegrations();
        assertEquals(1, integrations.size());

        IntegrationStatusResponse.IntegrationVersionStatus integration = integrations.get(0);
        assertEquals("test-integration", integration.getName());
        assertEquals("1.0", integration.getVersion());
        assertEquals(IntegrationStatus.UNREGISTERED, integration.getStatus());

        verify(serviceManager, times(1)).getDefinitionsByName("test-integration");
        verify(hipIntegrationRuntimeService, times(1)).getHIPIntegrationStatus(SERVICE_MANAGER_NAME, "test-integration", "1.0");
        verifyNoMoreInteractions(serviceManager, hipIntegrationRuntimeService);
    }





    // === Backward compatibility tests ===

    @Test
    void testSeparateStatusEndpoint_StillWorks() {
        // Arrange
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> expectedInfos = Arrays.asList(
                testInfoV1, testInfoV2, testInfoV3);

        when(orchestrationService.getAllHIPIntegrationsWithStatus())
                .thenReturn(expectedInfos);

        // Act
        ResponseEntity<String> response = controller.status("test-integration", "2.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("PAUSED", response.getBody());
        assertEquals(MediaType.TEXT_PLAIN, response.getHeaders().getContentType());

        verify(orchestrationService, times(1)).getAllHIPIntegrationsWithStatus();
        verifyNoMoreInteractions(orchestrationService);
    }

    @Test
    void testSeparateStatusEndpoint_NotFound() {
        // Arrange
        List<HIPIntegrationOrchestrationService.HIPIntegrationInfo> expectedInfos = Arrays.asList(testInfoV1);

        when(orchestrationService.getAllHIPIntegrationsWithStatus())
                .thenReturn(expectedInfos);

        // Act
        ResponseEntity<String> response = controller.status("non-existent", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("UNREGISTERED", response.getBody());
        assertEquals(MediaType.TEXT_PLAIN, response.getHeaders().getContentType());

        verify(orchestrationService, times(1)).getAllHIPIntegrationsWithStatus();
        verifyNoMoreInteractions(orchestrationService);
    }

    @Test
    void testSingleDefinitionEndpoint_StillWorks() {
        // Arrange
        when(serviceManager.getIntegrationDefinition("test-integration", "1.0"))
                .thenReturn(testDefinitionV1);

        // Act
        ResponseEntity<?> response = controller.getDefinition("test-integration", "1.0");

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof HIPIntegrationDefinition);
        HIPIntegrationDefinition returnedDef = (HIPIntegrationDefinition) response.getBody();
        assertEquals("test-integration", returnedDef.getHipIntegrationName());
        assertEquals("1.0", returnedDef.getVersion());

        verify(serviceManager).getIntegrationDefinition("test-integration", "1.0");
    }

    // === Helper methods ===

    private HIPIntegrationDefinition createTestDefinition(String name, String version) {
        HIPIntegrationDefinition definition = new HIPIntegrationDefinition();
        definition.setHipIntegrationName(name);
        definition.setVersion(version);
        definition.setServiceManagerName("test-service-manager");
        definition.setBusinessFlowName("test-flow");
        definition.setDescription("Test integration definition for " + name + " v" + version);
        definition.setOwner("test-team");
        return definition;
    }
}
