package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocTypeConfig {
	
	@JsonProperty("documentTypeName")
    private String name;
	@JsonProperty("documentTypeVersion")
    private String version;
	private int documentTypeId;
    private DocTypeRuleOperator docTypeRuleOperator = DocTypeRuleOperator.ALL; // ALL, ANY or ONE
    private String dataFormat;
    private List<DocTypeIdentifier> docTypeIdentifiers;
    private ValidationConfig validation;
    @JsonProperty("attributeMappings")
    private List<AttributeMapping> attributeMappings = new ArrayList<>();

    public List<AttributeMapping> getAttributeMappings() { return attributeMappings; }
    // Getters & Setters
    // ...
}
