package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class EDIFlowStepConfig extends FlowStepConfig {
	
	private List<DocTypeEDIConfig> docTypeConfigs = new ArrayList<>();
    private DefaultEDIConfig defaultConfig;
    
    @Data
    public static class DocTypeEDIConfig extends DocTypeConfig {
        private EDIBehavior behavior = EDIBehavior.PROCESS;
        private String senderInterchangeId;
    	private String senderGroupId;
    	private String receiverInterchangeId;
    	private String receiverGroupId;
    	private String receiverGroupControlNumberSeqStart;
    	private String receiverGroupControlNumberSeqEnd;
    	private String authorizationInformationQualifier;
    	private String securityInformationQualifier;
    	private String interchangeIdQualifier;
    	private String repetitionSeparator;
    	private String interchangeControlVersionNumber;
    	private String interchangeControlNumberStart;
    	private String acknowledgementRequested;
    	private String usageIndicator;
    }

    @Data
    public static class DefaultEDIConfig {
    	private EDIBehavior behavior = EDIBehavior.PROCESS;
    	private String senderInterchangeId;
    	private String senderGroupId;
    	private String receiverInterchangeId;
    	private String receiverGroupId;
    	private String receiverGroupControlNumberSeqStart;
    	private String receiverGroupControlNumberSeqEnd;
    	private String authorizationInformationQualifier;
    	private String securityInformationQualifier;
    	private String interchangeIdQualifier;
    	private String repetitionSeparator;
    	private String interchangeControlVersionNumber;
    	private String interchangeControlNumberStart;
    	private String acknowledgementRequested;
    	private String usageIndicator;
    }

    public enum EDIBehavior { PROCESS, SKIP, TERMINATE }
  

}
