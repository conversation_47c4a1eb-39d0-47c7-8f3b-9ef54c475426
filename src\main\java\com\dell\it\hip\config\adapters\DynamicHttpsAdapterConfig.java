package com.dell.it.hip.config.adapters;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicHttpsAdapterConfig extends AdapterConfig {
    // --- Authentication ---
    @JsonProperty("https.consumer.api.key.header")
    private String apiKeyHeader;

    @JsonProperty("https.consumer.api.key.value")
    private String apiKeyValue;

    @JsonProperty("https.consumer.oauth.required")
    private boolean oAuthRequired;

    // --- Header Extraction ---
    @JsonProperty("https.consumer.headers.to.extract")
    private List<String> headersToExtract;

    // --- Performance ---
    @JsonProperty("https.consumer.max.request.size.bytes")
    private Integer maxRequestSizeBytes;

    @JsonProperty("https.consumer.max.concurrency")
    private Integer maxConcurrency;

    @JsonProperty("https.consumer.request.timeout.ms")
    private Integer requestTimeoutMs; // per request, ms

    @JsonProperty("https.consumer.rate.limit.per.second")
    private Integer rateLimitPerSecond; // soft throttle

    // --- HTTP ---
    @JsonProperty("https.consumer.allowed.http.methods")
    private List<String> allowedHttpMethods;
}