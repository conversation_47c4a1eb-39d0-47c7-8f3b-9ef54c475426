package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonDeserialize(using = FlowTargetsRoutingConfigDeserializer.class)
public class FlowTargetsRoutingConfig extends FlowStepConfig implements RuleEnabledStepConfig {
    private List<RuleRef> ruleRefs;
    // ...other config properties...
    @JsonProperty("dbBacked")
    private boolean isDbBacked=true;

    public void setRuleRefs(List<RuleRef> ruleRefs) {
        this.ruleRefs = ruleRefs;
    }

    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }

    public boolean isDbBacked() {
        return isDbBacked;
    }

    public void setDbBacked(boolean dbBacked) {
        this.isDbBacked = dbBacked;
    }
}

class FlowTargetsRoutingConfigDeserializer extends JsonDeserializer<FlowTargetsRoutingConfig> {
	private static final Pattern DOT_NOTATION_PATTERN =
	        Pattern.compile("ruleRefs\\[(\\d+)\\]\\.(.+)");

	@Override
	public FlowTargetsRoutingConfig deserialize(JsonParser parser, DeserializationContext ctxt)
			throws IOException, JacksonException {


        JsonNode node = parser.getCodec().readTree(parser);
        FlowTargetsRoutingConfig config = new FlowTargetsRoutingConfig();

        // Map to collect AttributeMapping data by index
        Map<Integer, Map<String, String>> targetMappingData = new HashMap<>();

        // Iterate through all fields in the JSON
        Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            // Check if this field matches our dot notation pattern
            Matcher matcher = DOT_NOTATION_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                int index = Integer.parseInt(matcher.group(1));  // e.g., 0, 1, 2
                String propertyName = matcher.group(2);  // e.g., "attributeName", "derivedFrom"
                String value = fieldValue.asText();

                // Get or create the map for this index
                Map<String, String> mappingData = targetMappingData.computeIfAbsent(index, k -> new HashMap<>());
                mappingData.put(propertyName, value);
            } else {
                // Handle regular properties
                switch (fieldName) {
                    case "dbBacked":
                        config.setDbBacked(fieldValue.asBoolean());
                        break;                        
                    case "type":
                        config.setType(fieldValue.asText());
                        break;
                    case "id":
                        config.setId(fieldValue.asText());
                        break;
                    case "role":
                        config.setRole(fieldValue.asText());
                        break;
                }
            }
        }

        // Convert the collected data into List<AttributeMapping>
        if (!targetMappingData.isEmpty()) {
            List<RuleRef> reuleRefsMapping = new ArrayList<>();

            // Process indices in order (0, 1, 2, ...)
            int maxIndex = targetMappingData.keySet().stream().mapToInt(Integer::intValue).max().orElse(-1);
            for (int i = 0; i <= maxIndex; i++) {
                Map<String, String> reuleRefData = targetMappingData.get(i);
                if (reuleRefData != null) {
                	RuleRef mapping = new RuleRef();

                    // Set properties from the collected data
                    if (reuleRefData.containsKey("name")) {
                        mapping.setRuleName(reuleRefData.get("name"));
                    }
                    if (reuleRefData.containsKey("version")) {
                        mapping.setRuleVersion(reuleRefData.get("version"));
                    }

                    reuleRefsMapping.add(mapping);
                } else {
                    // Add null for missing indices to maintain order
                	reuleRefsMapping.add(null);
                }
            }

            config.setRuleRefs(reuleRefsMapping);
        }

        return config;
    
	}
	
}