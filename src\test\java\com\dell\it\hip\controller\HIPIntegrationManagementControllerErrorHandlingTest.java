package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationNotFoundException;
import com.dell.it.hip.exception.IntegrationOperationException;
import com.dell.it.hip.exception.IntegrationRegistrationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

/**
 * Unit tests for HIPIntegrationManagementController error handling.
 * Tests that exceptions are properly propagated from the service layer.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerErrorHandlingTest {

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationRequest testRequest;

    @BeforeEach
    void setUp() {
        // Set the service manager name using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", "test-service-manager");

        testRequest = new HIPIntegrationRequest();
        testRequest.setHipIntegrationName("test-integration");
        testRequest.setVersion("1.0");
    }

    @Test
    void testRegisterIntegration_Success() throws Exception {
        // Arrange
        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString())).thenReturn(false);

        // Act
        ResponseEntity<?> response = controller.register(testRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("HIPIntegration registered", response.getBody());
    }

    @Test
    void testRegisterIntegration_ThrowsIntegrationRegistrationException() throws Exception {
        // Arrange
        doThrow(new IntegrationRegistrationException("test-integration", "1.0", "Configuration invalid"))
                .when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act & Assert
        IntegrationRegistrationException exception = assertThrows(IntegrationRegistrationException.class, () -> {
            controller.register(testRequest);
        });

        assertTrue(exception.getMessage().contains("test-integration"));
        assertTrue(exception.getMessage().contains("1.0"));
        assertEquals("test-integration", exception.getIntegrationName());
        assertEquals("1.0", exception.getVersion());
    }

    @Test
    void testUnregisterIntegration_ThrowsIntegrationNotFoundException() throws Exception {
        // Arrange
        doThrow(new IntegrationNotFoundException("test-integration", "1.0"))
                .when(orchestrationService).unregisterHIPIntegration(anyString(), anyString());

        // Act & Assert
        IntegrationNotFoundException exception = assertThrows(IntegrationNotFoundException.class, () -> {
            controller.unregister("test-integration", "1.0");
        });

        assertTrue(exception.getMessage().contains("test-integration"));
        assertTrue(exception.getMessage().contains("1.0"));
    }

    @Test
    void testUnregisterIntegration_ThrowsIntegrationOperationException() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("unregister", "test-integration", "1.0", "Failed to shutdown adapters"))
                .when(orchestrationService).unregisterHIPIntegration(anyString(), anyString());

        // Act & Assert
        IntegrationOperationException exception = assertThrows(IntegrationOperationException.class, () -> {
            controller.unregister("test-integration", "1.0");
        });

        assertTrue(exception.getMessage().contains("unregister"));
        assertTrue(exception.getMessage().contains("test-integration"));
        assertEquals("unregister", exception.getOperation());
        assertEquals("test-integration", exception.getIntegrationName());
        assertEquals("1.0", exception.getVersion());
    }

    @Test
    void testPauseIntegration_ThrowsIntegrationOperationException() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("pause", "test-integration", "1.0", "Failed to pause adapters"))
                .when(orchestrationService).pauseHIPIntegration(anyString(), anyString());

        // Act & Assert
        IntegrationOperationException exception = assertThrows(IntegrationOperationException.class, () -> {
            controller.pause("test-integration", "1.0");
        });

        assertTrue(exception.getMessage().contains("pause"));
        assertEquals("pause", exception.getOperation());
        assertEquals("test-integration", exception.getIntegrationName());
    }

    @Test
    void testResumeIntegration_ThrowsIntegrationOperationException() throws Exception {
        // Arrange
        doThrow(new IntegrationOperationException("resume", "test-integration", "1.0", "Failed to resume handlers"))
                .when(orchestrationService).resumeHIPIntegration(anyString(), anyString());

        // Act & Assert
        IntegrationOperationException exception = assertThrows(IntegrationOperationException.class, () -> {
            controller.resume("test-integration", "1.0");
        });

        assertTrue(exception.getMessage().contains("resume"));
        assertEquals("resume", exception.getOperation());
        assertEquals("test-integration", exception.getIntegrationName());
    }
}