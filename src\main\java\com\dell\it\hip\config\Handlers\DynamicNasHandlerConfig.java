package com.dell.it.hip.config.Handlers;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicNasHandlerConfig extends HandlerConfig {

    @JsonProperty("nas.producer.protocol")
    private String protocol;              // "smb" or "nfs"

    @JsonProperty("nas.producer.host")
    private String host;                  // Host/IP for SMB; not needed for NFS if local mount

    @JsonProperty("nas.producer.mount.path")
    private String remoteDirectory;       // Directory/share/subpath (for SMB: path within share; for NFS: local mount path)

    @JsonProperty("nas.producer.file.separator")
    private String fileSeparator = "/";   // "/" for NFS, usually "\\" for SMB

    @JsonProperty("nas.producer.gzip.enabled")
    private Boolean gzipEnabled;

    // SMB-specific
    @JsonProperty("nas.producer.share.name")
    private String shareName;             // Required for SMB (e.g., "SHARE" in \\host\SHARE\path\to\file)

    @JsonProperty("nas.producer.username")
    private String username;

    @JsonProperty("nas.producer.password")
    private String password;

    @JsonProperty("nas.producer.domain")
    private String domain;                // Optional: For Active Directory/Kerberos
}