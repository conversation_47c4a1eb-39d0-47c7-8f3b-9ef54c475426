package com.dell.it.hip.config.adapters;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicSFTPAdapterConfig extends AdapterConfig {

	@JsonProperty("sftp.consumer.host")
	private String host;

	@JsonProperty("sftp.consumer.port")
    private Integer port;

	@JsonProperty("sftp.consumer.username")
    private String username;

	@JsonProperty("sftp.consumer.password")
    private String password;

    @JsonProperty("sftp.consumer.private.key")
    private String privateKey;

    @JsonProperty("sftp.consumer.private.key.passphrase")
    private String privateKeyPassphrase;

	@JsonProperty("sftp.consumer.remote.directory")
    private String remoteDirectory;

	@JsonProperty("sftp.consumer.file.filter")
    private String fileNamePattern;

    @JsonProperty("sftp.consumer.polling.interval.ms")
    private Long pollingIntervalMs = 60000L;

    @JsonProperty("sftp.consumer.compressed")
    private boolean compressed = false;

    @JsonProperty("sftp.consumer.charset")
    private String charset = "UTF-8";

    @JsonProperty("sftp.consumer.headers.to.extract")
    private List<String> headersToExtract;

    @JsonProperty("sftp.consumer.post.process.action")
    private String postProcessAction; // delete/rename

    @JsonProperty("sftp.consumer.rename.pattern")
    private String renamePattern;
}