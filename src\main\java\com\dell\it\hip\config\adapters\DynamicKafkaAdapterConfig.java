package com.dell.it.hip.config.adapters;


import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Configuration class for Kafka adapters.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicKafkaAdapterConfig extends AdapterConfig {

		@JsonProperty("kafka.consumer.bootstrap.servers")
        private String bootstrapServers;
        
        @JsonProperty("kafka.consumer.topic.name")
        private String topic;

        @JsonProperty("kafka.consumer.client.id")
        private String clientId;

        @JsonProperty("kafka.consumer.group.id")
        private String groupId;

        @JsonProperty("kafka.consumer.concurrency")
        private Integer concurrency;

        @JsonProperty("kafka.consumer.max.poll.records")
        private Integer maxPollRecords;

        @JsonProperty("kafka.consumer.auth.type")
        private String authenticationType;

		@JsonProperty("kafka.consumer.security.protocol")
        private String securityProtocol;

		@JsonProperty("kafka.consumer.sasl.mechanism")
        private String saslMechanism;

        @JsonProperty("kafka.consumer.username")
        private String username;

        @JsonProperty("kafka.consumer.password")
        private String password;

        @JsonProperty("kafka.consumer.auto.offset.reset")
        private String autoOffsetReset;

        @JsonProperty("kafka.consumer.fetch.min.bytes")
        private Integer fetchMinBytes;

        @JsonProperty("kafka.consumer.fetch.max.bytes")
        private Integer fetchMaxBytes;

        @JsonProperty("kafka.consumer.max.partition.fetch.bytes")
        private Integer maxPartitionFetchBytes;

        @JsonProperty("kafka.consumer.session.timeout.ms")
        private Integer sessionTimeoutMs;

        @JsonProperty("kafka.consumer.heartbeat.interval.ms")
        private Integer heartbeatIntervalMs;

        @JsonProperty("kafka.consumer.poll.timeout.ms")
        private Integer pollTimeoutMs;

        @JsonProperty("kafka.consumer.enable.auto.commit")
        private Boolean enableAutoCommit;

        @JsonProperty("kafka.consumer.auto.commit.interval.ms")
        private Integer autoCommitIntervalMs;

        @JsonProperty("kafka.consumer.max.poll.interval.ms")
        private Integer maxPollIntervalMs;

        @JsonProperty("kafka.consumer.request.timeout.ms")
        private Integer requestTimeoutMs;

        @JsonProperty("kafka.consumer.retries")
        private Integer retries;

        @JsonProperty("kafka.consumer.retry.backoff.ms")
        private Integer retryBackoffMs;

        @JsonProperty("kafka.consumer.isolation.level")
        private String isolationLevel;

        @JsonProperty("kafka.consumer.allow.auto.create.topics")
        private Boolean allowAutoCreateTopics;

		@JsonProperty("kafka.consumer.ssl.truststore.location")
        private String sslTruststoreLocation;

        @JsonProperty("kafka.consumer.ssl.truststore.password")
        private String sslTruststorePassword;

        @JsonProperty("kafka.consumer.ssl.keystore.location")
        private String sslKeystoreLocation;

        @JsonProperty("kafka.consumer.ssl.keystore.password")
        private String sslKeystorePassword;

        @JsonProperty("kafka.consumer.ssl.key.password")
        private String sslKeyPassword;

        @JsonProperty("kafka.consumer.enrich.headers")
        private Map<String, String> enrichHeaders;

        @JsonProperty("kafka.consumer.value.deserializer")
        private String valueDeserializer;

        @JsonProperty("kafka.consumer.key.deserializer")
        private String keyDeserializer;

        @JsonProperty("kafka.consumer.headers.to.extract")
        public List<String> HeadersToExtract;

        @JsonProperty("kafka.consumer.properties")
        private Map<String, ?> properties; // For arbitrary extra settings

        @JsonProperty("kafka.consumer.compressed")
        private boolean compressed = false;
       
       public String getBootstrapServers() {
        return bootstrapServers;
    }

    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Integer getConcurrency() {
        return concurrency;
    }

    public void setConcurrency(Integer concurrency) {
        this.concurrency = concurrency;
    }

    public Integer getMaxPollRecords() {
        return maxPollRecords;
    }

    public void setMaxPollRecords(Integer maxPollRecords) {
        this.maxPollRecords = maxPollRecords;
    }

    public String getAuthenticationType() {
        return authenticationType;
    }

    public void setAuthenticationType(String authenticationType) {
        this.authenticationType = authenticationType;
    }

    public String getSecurityProtocol() {
        return securityProtocol;
    }

    public void setSecurityProtocol(String securityProtocol) {
        this.securityProtocol = securityProtocol;
    }

    public String getSaslMechanism() {
        return saslMechanism;
    }

    public void setSaslMechanism(String saslMechanism) {
        this.saslMechanism = saslMechanism;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getAutoOffsetReset() {
        return autoOffsetReset;
    }

    public void setAutoOffsetReset(String autoOffsetReset) {
        this.autoOffsetReset = autoOffsetReset;
    }

    public Integer getFetchMinBytes() {
        return fetchMinBytes;
    }

    public void setFetchMinBytes(Integer fetchMinBytes) {
        this.fetchMinBytes = fetchMinBytes;
    }

    public Integer getFetchMaxBytes() {
        return fetchMaxBytes;
    }

    public void setFetchMaxBytes(Integer fetchMaxBytes) {
        this.fetchMaxBytes = fetchMaxBytes;
    }

    public Integer getMaxPartitionFetchBytes() {
        return maxPartitionFetchBytes;
    }

    public void setMaxPartitionFetchBytes(Integer maxPartitionFetchBytes) {
        this.maxPartitionFetchBytes = maxPartitionFetchBytes;
    }

    public Integer getSessionTimeoutMs() {
        return sessionTimeoutMs;
    }

    public void setSessionTimeoutMs(Integer sessionTimeoutMs) {
        this.sessionTimeoutMs = sessionTimeoutMs;
    }

    public Integer getHeartbeatIntervalMs() {
        return heartbeatIntervalMs;
    }

    public void setHeartbeatIntervalMs(Integer heartbeatIntervalMs) {
        this.heartbeatIntervalMs = heartbeatIntervalMs;
    }

    public Integer getPollTimeoutMs() {
        return pollTimeoutMs;
    }

    public void setPollTimeoutMs(Integer pollTimeoutMs) {
        this.pollTimeoutMs = pollTimeoutMs;
    }

    public Boolean getEnableAutoCommit() {
        return enableAutoCommit;
    }

    public void setEnableAutoCommit(Boolean enableAutoCommit) {
        this.enableAutoCommit = enableAutoCommit;
    }

    public Integer getAutoCommitIntervalMs() {
        return autoCommitIntervalMs;
    }

    public void setAutoCommitIntervalMs(Integer autoCommitIntervalMs) {
        this.autoCommitIntervalMs = autoCommitIntervalMs;
    }

    public Integer getMaxPollIntervalMs() {
        return maxPollIntervalMs;
    }

    public void setMaxPollIntervalMs(Integer maxPollIntervalMs) {
        this.maxPollIntervalMs = maxPollIntervalMs;
    }

    public Integer getRequestTimeoutMs() {
        return requestTimeoutMs;
    }

    public void setRequestTimeoutMs(Integer requestTimeoutMs) {
        this.requestTimeoutMs = requestTimeoutMs;
    }

    public Integer getRetries() {
        return retries;
    }

    public void setRetries(Integer retries) {
        this.retries = retries;
    }

    public Integer getRetryBackoffMs() {
        return retryBackoffMs;
    }

    public void setRetryBackoffMs(Integer retryBackoffMs) {
        this.retryBackoffMs = retryBackoffMs;
    }

    public String getIsolationLevel() {
        return isolationLevel;
    }

    public void setIsolationLevel(String isolationLevel) {
        this.isolationLevel = isolationLevel;
    }

    public Boolean getAllowAutoCreateTopics() {
        return allowAutoCreateTopics;
    }

    public void setAllowAutoCreateTopics(Boolean allowAutoCreateTopics) {
        this.allowAutoCreateTopics = allowAutoCreateTopics;
    }

    public String getSslTruststoreLocation() {
        return sslTruststoreLocation;
    }

    public void setSslTruststoreLocation(String sslTruststoreLocation) {
        this.sslTruststoreLocation = sslTruststoreLocation;
    }

    public String getSslTruststorePassword() {
        return sslTruststorePassword;
    }

    public void setSslTruststorePassword(String sslTruststorePassword) {
        this.sslTruststorePassword = sslTruststorePassword;
    }

    public String getSslKeystoreLocation() {
        return sslKeystoreLocation;
    }

    public void setSslKeystoreLocation(String sslKeystoreLocation) {
        this.sslKeystoreLocation = sslKeystoreLocation;
    }

    public String getSslKeystorePassword() {
        return sslKeystorePassword;
    }

    public void setSslKeystorePassword(String sslKeystorePassword) {
        this.sslKeystorePassword = sslKeystorePassword;
    }

    public String getSslKeyPassword() {
        return sslKeyPassword;
    }

    public void setSslKeyPassword(String sslKeyPassword) {
        this.sslKeyPassword = sslKeyPassword;
    }

    public String getValueDeserializer() {
        return valueDeserializer;
    }

    public void setValueDeserializer(String valueDeserializer) {
        this.valueDeserializer = valueDeserializer;
    }

    public String getKeyDeserializer() {
        return keyDeserializer;
    }

    public void setKeyDeserializer(String keyDeserializer) {
        this.keyDeserializer = keyDeserializer;
    }

    public Map<String, String> getEnrichHeaders() {
        return enrichHeaders;
    }

    public void setEnrichHeaders(Map<String, String> enrichHeaders) {
        this.enrichHeaders = enrichHeaders;
    }

    public boolean isCompressed() {
        return compressed;
    }

    public void setCompressed(boolean compressed) {
        this.compressed = compressed;
    }

    public Map<String, ?> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, ?> properties) {
        this.properties = properties;
    }

    public List<String> getHeadersToExtract() {
        return HeadersToExtract;
    }
    public void setHeadersToExtract(List<String> headersToExtract) {
        HeadersToExtract = headersToExtract;
    }

    // Getters and setters...

}