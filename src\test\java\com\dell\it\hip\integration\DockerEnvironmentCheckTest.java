package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple test to verify Docker environment checking functionality.
 * This test does not extend BaseIntegrationTest to avoid Docker dependencies.
 */
class DockerEnvironmentCheckTest {

    private static final Logger logger = LoggerFactory.getLogger(DockerEnvironmentCheckTest.class);

    @Test
    void testDockerAvailabilityCheck() {
        logger.info("Testing Docker availability check method...");
        
        // This should not throw an exception, regardless of Docker availability
        boolean dockerAvailable = BaseIntegrationTest.isDockerAvailable();
        boolean dockerNotAvailable = BaseIntegrationTest.isDockerNotAvailable();
        
        logger.info("Docker available: {}", dockerAvailable);
        logger.info("Docker not available: {}", dockerNotAvailable);
        
        // These methods should always return opposite values
        assert dockerAvailable != dockerNotAvailable : "isDockerAvailable and isDockerNotAvailable should return opposite values";
        
        // The methods should not be null (they should return boolean values)
        assertNotNull(dockerAvailable);
        assertNotNull(dockerNotAvailable);
        
        if (!dockerAvailable) {
            logger.warn("Docker is not available - integration tests should be skipped");
            logger.warn("This is the expected behavior when Docker is not running");
        } else {
            logger.info("Docker is available - integration tests can run");
        }
        
        logger.info("✓ Docker environment check methods are working correctly");
    }
}
