package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ValidationFlowStepConfig extends FlowStepConfig {
    private List<DocTypeValidationConfig> docTypeConfigs = new ArrayList<>();
    private DefaultValidationConfig defaultConfig;

    @Data
    public static class DocTypeValidationConfig extends DocTypeConfig {
        private ValidationBehavior validationBehavior = ValidationBehavior.STRUCTURAL;
        private String schemaKey;       // Optionally provide per-doctype schema
        private String schemaType;
        //private ValidationConfig validation; // For future extension (optional)
    }

    @Data
    public static class DefaultValidationConfig {
        private ValidationBehavior validationBehavior = ValidationBehavior.STRUCTURAL;
        private String schemaKey;
        private String schemaType;
        private ValidationConfig validation; // For future extension (optional)
    }

    @Data
    public static class ValidationConfig {
        private boolean structural;
        private boolean schema;
        private String schemaType;      // e.g. "XSD", "JSONSCHEMA", "STAEDI"
        private String schemaKey;       // Redis key for schema
        // ...any more
    }

    public enum ValidationBehavior {
        SCHEMA, STRUCTURAL, SKIP, TERMINATE
    }
}