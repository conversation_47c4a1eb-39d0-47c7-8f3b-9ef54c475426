package com.dell.it.hip.config.rules;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Rule {
    private String ruleName;
    private String ruleDescription;
    private String documentType;
    private boolean executeAlways;
    private boolean enabled;
    private RuleStatus status;
    private ExecuteActionWhen executeActionWhen;
    private List<RuleCondition> ruleConditions;
    private List<RuleActionDescriptor> actions; // Store descriptors; build actual RuleAction at runtime
    private RuleScope ruleScope;
    private String ruleType;
    private String ruleVersion;
    public enum RuleStatus { ENABLED, DISABLED }
    public enum ExecuteActionWhen { ALL, ANY }
    public enum Operator { EQUALS, STARTS_WITH, CONTAINS }
    public enum RuleScope { GLOBAL, LOCAL }

    // Getters, setters, constructors
}