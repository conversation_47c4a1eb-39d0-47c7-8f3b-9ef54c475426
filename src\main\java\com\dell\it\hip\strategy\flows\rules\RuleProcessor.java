package com.dell.it.hip.strategy.flows.rules;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.config.rules.RuleActionDescriptor;
import com.dell.it.hip.config.rules.RuleCondition;
import com.dell.it.hip.config.rules.RuleRef;
import com.dell.it.hip.core.registry.RuleActionRegistry;
import com.dell.it.hip.util.logging.WiretapService;

@Service
public class RuleProcessor {

    @Autowired private RuleActionRegistry actionRegistry;
    @Autowired private RuleCache ruleCache;
    @Autowired private WiretapService wiretapService;
    // @Autowired private TransactionLoggingUtil transactionLogger; // Uncomment if using

    // For FlowRoutingFlowStepStrategy (integration rules loaded by prefix)
    public Message<?> processRules(
            Message<?> message,
            HIPIntegrationDefinition def,
            FlowStepConfigRef stepConfigRef,
            Map<String, Object> context
    ) {
        String serviceManager = def.getServiceManagerName();
        String integration = def.getHipIntegrationName();
        String version = def.getVersion();
        List<Rule> rules = ruleCache.getRulesForIntegration(serviceManager, integration, version);
        // if (transactionLogger != null) transactionLogger.logRuleFetch("prefix", serviceManager, integration, version, rules.size());
        return applyRules(message, rules, def, stepConfigRef, context);
    }

    // For MappingTransformerFlowStepStrategy & FlowTargetsRoutingFlowStepStrategy (explicit list from property sheet)
    public Message<?> processRules(
            Message<?> message,
            List<RuleRef> ruleRefs,
            HIPIntegrationDefinition def,
            FlowStepConfigRef stepConfigRef,
            Map<String, Object> context,
            boolean dbBacked
    ) {
        List<Rule> rules = ruleCache.getRulesByRefs(ruleRefs, dbBacked);
        // if (transactionLogger != null) transactionLogger.logRuleFetch("refs", ruleRefs, dbBacked, rules.size());
        return applyRules(message, rules, def, stepConfigRef, context);
    }

    private Message<?> applyRules(
            Message<?> message,
            List<Rule> rules,
            HIPIntegrationDefinition def,
            FlowStepConfigRef stepConfigRef,
            Map<String, Object> context
    ) {
        if (rules == null || rules.isEmpty()) {
            wiretapService.tap(
                    message, def, stepConfigRef, "terminated",
                    "No rules found for step [" + (stepConfigRef != null ? stepConfigRef.getPropertyRef() : null) + "]. Message terminated."
            );
            // if (transactionLogger != null) transactionLogger.logEvent("RuleProcessor", "terminated", stepConfigRef, message);
            return message;
        }
        for (Rule rule : rules) {
            if (rule == null) continue;
            wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Start: " + rule.getRuleName());
            // if (transactionLogger != null) transactionLogger.logEvent("RuleProcessor", "start", stepConfigRef, rule);

            if (rule.getStatus() != Rule.RuleStatus.ENABLED) {
                wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Skipped (disabled): " + rule.getRuleName());
                continue;
            }

            String docType = (String) message.getHeaders().get("HIP.documentType");
            if (rule.getDocumentType() != null &&
                    !"any".equalsIgnoreCase(rule.getDocumentType()) &&
                    !rule.getDocumentType().equalsIgnoreCase(docType)) {
                wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Skipped (docType mismatch): " + rule.getRuleName());
                continue;
            }

            boolean execute = rule.isExecuteAlways() || evaluateConditions(rule, message, def, stepConfigRef);

            if (!execute) {
                wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Skipped (conditions false): " + rule.getRuleName());
                continue;
            }

            for (RuleActionDescriptor desc : rule.getActions()) {
                RuleAction action = actionRegistry.createAction(desc);
                try {
                    wiretapService.tap(message, def, stepConfigRef, "info", "[ACTION] Executing: " + desc.getType() + " for rule: " + rule.getRuleName());
                    // if (transactionLogger != null) transactionLogger.logAction("execute", desc, rule);
                    message = action.performAction(message, desc.getParams(), rule, def, context);
                    wiretapService.tap(message, def, stepConfigRef, "info", "[ACTION] Completed: " + desc.getType() + " for rule: " + rule.getRuleName());
                } catch (Exception ex) {
                    wiretapService.tap(message, def, stepConfigRef, "error", "[ACTION] Failed: " + desc.getType() + " for rule: " + rule.getRuleName() + " -- " + ex.getMessage());
                    // if (transactionLogger != null) transactionLogger.logAction("error", desc, rule, ex);
                }
                if (Boolean.TRUE.equals(context.get("stopRuleProcessing"))) {
                    wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Stop-rule reached for rule: " + rule.getRuleName());
                    // if (transactionLogger != null) transactionLogger.logEvent("RuleProcessor", "stop", stepConfigRef, rule);
                    message = MessageBuilder.fromMessage(message).copyHeaders(context).build();
                    return message;
                }
            }
            wiretapService.tap(message, def, stepConfigRef, "info", "[RULE] Completed: " + rule.getRuleName());
        }
        message = MessageBuilder.fromMessage(message)
                .copyHeaders(context)
                .build();
        return message;
    }

    private boolean evaluateConditions(Rule rule, Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef) {
        if (rule.getRuleConditions() == null || rule.getRuleConditions().isEmpty()) return true;
        boolean andLogic = rule.getExecuteActionWhen() == Rule.ExecuteActionWhen.ALL;
        for (RuleCondition cond : rule.getRuleConditions()) {
        	String docType = (String) message.getHeaders().get("HIP.documentType");
            String docTypever = (String) message.getHeaders().get("HIP.documentTypeVersion");
            Object headerVal = message.getHeaders().get(cond.getPropertyName());
            boolean match = false;
            if (headerVal != null) {
                String val = String.valueOf(headerVal);
                switch (cond.getOperation()) {
                    case "EQUALS": match = val.equals(cond.getValue()); break;
                    case "STARTS_WITH": match = val.startsWith(cond.getValue()); break;
                    case "CONTAINS": match = val.contains(cond.getValue()); break;
                }
            }
            wiretapService.tap(message, def, stepConfigRef, "info", "[CONDITION] " + cond.getOperation() + " " + cond.getPropertyName() + " -> " + match);
            // if (transactionLogger != null) transactionLogger.logCondition(cond, match);
            if (andLogic && !match) return false;
            if (!andLogic && match) return true;
        }
        return andLogic;
    }
}