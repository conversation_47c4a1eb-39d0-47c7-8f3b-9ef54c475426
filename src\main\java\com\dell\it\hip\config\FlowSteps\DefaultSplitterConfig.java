package com.dell.it.hip.config.FlowSteps;

import lombok.Data;

// Default fallback config
@Data
public class DefaultSplitterConfig extends SplitFlowConfig {
    private String action; // "ignore", "regex_split", "terminate"
    private String regexExpression;

    public DefaultSplitterConfig() {
        setName("default");
        setVersion("default");
        setDataFormat("default");
    }
    // Getters & Setters...
}