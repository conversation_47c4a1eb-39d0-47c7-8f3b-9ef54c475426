package com.dell.it.hip.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class IntegrationDuplicateExceptionTest {

    @Test
    void testBasicConstructor() {
        String message = "Duplicate integration";
        IntegrationDuplicateException exception = new IntegrationDuplicateException(message);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getServiceManagerName());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
    }

    @Test
    void testConstructorWithCause() {
        String message = "Duplicate integration";
        RuntimeException cause = new RuntimeException("Root cause");
        IntegrationDuplicateException exception = new IntegrationDuplicateException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertNull(exception.getServiceManagerName());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
    }

    @Test
    void testConstructorWithIntegrationDetails() {
        String serviceManagerName = "test-service-manager";
        String integrationName = "test-integration";
        String version = "1.0";
        
        IntegrationDuplicateException exception = new IntegrationDuplicateException(
            serviceManagerName, integrationName, version);
        
        String expectedMessage = "Integration with service manager 'test-service-manager', integration 'test-integration', and version '1.0' already exists";
        assertEquals(expectedMessage, exception.getMessage());
        assertEquals(serviceManagerName, exception.getServiceManagerName());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
    }

    @Test
    void testConstructorWithIntegrationDetailsAndMessage() {
        String serviceManagerName = "test-service-manager";
        String integrationName = "test-integration";
        String version = "1.0";
        String additionalMessage = "Additional context";
        
        IntegrationDuplicateException exception = new IntegrationDuplicateException(
            serviceManagerName, integrationName, version, additionalMessage);
        
        assertTrue(exception.getMessage().contains(serviceManagerName));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains(additionalMessage));
        assertEquals(serviceManagerName, exception.getServiceManagerName());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
    }

    @Test
    void testConstructorWithIntegrationDetailsAndCause() {
        String serviceManagerName = "test-service-manager";
        String integrationName = "test-integration";
        String version = "1.0";
        RuntimeException cause = new RuntimeException("Root cause");
        
        IntegrationDuplicateException exception = new IntegrationDuplicateException(
            serviceManagerName, integrationName, version, cause);
        
        assertTrue(exception.getMessage().contains(serviceManagerName));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains("Root cause"));
        assertEquals(serviceManagerName, exception.getServiceManagerName());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testMessageFormatting() {
        String serviceManagerName = "my-service";
        String integrationName = "payment-processor";
        String version = "2.1.0";
        
        IntegrationDuplicateException exception = new IntegrationDuplicateException(
            serviceManagerName, integrationName, version);
        
        String message = exception.getMessage();
        assertTrue(message.contains("'my-service'"));
        assertTrue(message.contains("'payment-processor'"));
        assertTrue(message.contains("'2.1.0'"));
        assertTrue(message.contains("already exists"));
    }

    @Test
    void testNullValuesHandling() {
        IntegrationDuplicateException exception = new IntegrationDuplicateException(null, null, null);
        
        String message = exception.getMessage();
        assertTrue(message.contains("'null'"));
        assertNull(exception.getServiceManagerName());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
    }
}
