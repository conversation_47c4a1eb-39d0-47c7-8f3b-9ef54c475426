package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assumptions.assumeTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Integration tests for Redis functionality using TestContainers.
 * Extends BaseIntegrationTest for shared container setup and Docker validation.
 * Tests are automatically skipped when Redis is disabled.
 */
@EnabledIf("com.dell.it.hip.integration.BaseIntegrationTest#isDockerAvailable")
class RedisIntegrationTest extends BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisIntegrationTest.class);

    @Autowired(required = false)
    private StringRedisTemplate redisTemplate;

    @Value("${spring.data.redis.enabled:true}")
    private boolean redisEnabled;

    @BeforeEach
    void setUp() {
        // Skip setup if Redis is disabled
        if (!redisEnabled || redisTemplate == null) {
            logger.info("Redis is disabled or not available - skipping Redis setup");
            return;
        }

        // Clear Redis before each test
        try {
            redisTemplate.getConnectionFactory().getConnection().serverCommands().flushAll();
            logger.debug("Redis cleared successfully before test");
        } catch (Exception e) {
            logger.warn("Failed to clear Redis before test: {}", e.getMessage());
        }
    }

    @Test
    void testRedisConnection() {
        // Skip test if Redis is disabled or not available
        assumeTrue(redisEnabled && redisTemplate != null,
                   "Redis is disabled or not available - skipping Redis connection test");

        logger.info("Testing Redis connection...");

        // Test basic Redis connectivity
        String key = "test:connection";
        String value = "connected";

        redisTemplate.opsForValue().set(key, value);
        String retrieved = redisTemplate.opsForValue().get(key);

        assertEquals(value, retrieved);
        logger.info("✓ Redis connection test passed");
    }

    @Test
    void testHashOperations() {
        // Skip test if Redis is disabled or not available
        assumeTrue(redisEnabled && redisTemplate != null,
                   "Redis is disabled or not available - skipping Redis hash operations test");

        logger.info("Testing Redis hash operations...");

        // Test hash operations for complex data structures
        String hashKey = "hip:test-service:config:test-integration:1.0";

        redisTemplate.opsForHash().put(hashKey, "maxRequests", "100");
        redisTemplate.opsForHash().put(hashKey, "timeWindow", "60");
        redisTemplate.opsForHash().put(hashKey, "enabled", "true");

        assertEquals("100", redisTemplate.opsForHash().get(hashKey, "maxRequests"));
        assertEquals("60", redisTemplate.opsForHash().get(hashKey, "timeWindow"));
        assertEquals("true", redisTemplate.opsForHash().get(hashKey, "enabled"));

        // Test hash size
        assertEquals(3L, redisTemplate.opsForHash().size(hashKey));
        logger.info("✓ Redis hash operations test passed");
    }

    @Test
    void testListOperations() {
        // Skip test if Redis is disabled or not available
        assumeTrue(redisEnabled && redisTemplate != null,
                   "Redis is disabled or not available - skipping Redis list operations test");

        logger.info("Testing Redis list operations...");

        // Test list operations for message queues
        String listKey = "hip:test-service:queue:test-integration:1.0";

        // Push messages to queue
        redisTemplate.opsForList().leftPush(listKey, "message1");
        redisTemplate.opsForList().leftPush(listKey, "message2");
        redisTemplate.opsForList().leftPush(listKey, "message3");

        // Check queue size
        assertEquals(3L, redisTemplate.opsForList().size(listKey));

        // Pop messages from queue
        assertEquals("message3", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message2", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message1", redisTemplate.opsForList().rightPop(listKey));

        // Verify queue is empty
        assertEquals(0L, redisTemplate.opsForList().size(listKey));
        logger.info("✓ Redis list operations test passed");
    }

    @Test
    void testSetOperations() {
        // Skip test if Redis is disabled or not available
        assumeTrue(redisEnabled && redisTemplate != null,
                   "Redis is disabled or not available - skipping Redis set operations test");

        logger.info("Testing Redis set operations...");

        // Test set operations for unique collections
        String setKey = "hip:test-service:active-integrations";

        redisTemplate.opsForSet().add(setKey, "integration1:1.0");
        redisTemplate.opsForSet().add(setKey, "integration2:1.0");
        redisTemplate.opsForSet().add(setKey, "integration1:1.0"); // Duplicate

        // Verify set size (duplicates not counted)
        assertEquals(2L, redisTemplate.opsForSet().size(setKey));

        // Test membership
        assertTrue(redisTemplate.opsForSet().isMember(setKey, "integration1:1.0"));
        assertTrue(!redisTemplate.opsForSet().isMember(setKey, "integration3:1.0"));

        // Remove member
        redisTemplate.opsForSet().remove(setKey, "integration1:1.0");
        assertEquals(1L, redisTemplate.opsForSet().size(setKey));
        logger.info("✓ Redis set operations test passed");
    }
}
