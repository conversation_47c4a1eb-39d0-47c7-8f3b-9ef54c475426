package com.dell.it.hip.config.FlowSteps;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.node.ObjectNode;

@JsonDeserialize(using = SplitterFlowStepConfigDeserializer.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SplitterFlowStepConfig extends FlowStepConfig {

    private String stepName;

    // List of docType/dataFormat configs for this splitter
    @JsonProperty("configurationList")
    private List<SplitFlowConfig> docTypeSplitConfigs = new ArrayList<>();

    // Default fallback
    private DefaultSplitterConfig defaultConfig;

    private boolean copyHeaders = true;
    private boolean genericSplitting;
	private String splitterRegex;

    // Getters & Setters...
    public String getStepName() { return stepName; }
    public void setStepName(String stepName) { this.stepName = stepName; }
    public List<SplitFlowConfig> getDocTypeSplitConfigs() { return docTypeSplitConfigs; }
    public void setDocTypeSplitConfigs(List<SplitFlowConfig> docTypeSplitConfigs) { this.docTypeSplitConfigs = docTypeSplitConfigs; }
    public DefaultSplitterConfig getDefaultConfig() { return defaultConfig; }
    public void setDefaultConfig(DefaultSplitterConfig defaultConfig) { this.defaultConfig = defaultConfig; }
    public boolean isCopyHeaders() { return copyHeaders; }
    public void setCopyHeaders(boolean copyHeaders) { this.copyHeaders = copyHeaders; }
    public SplitFlowConfig findBestConfig(String docTypeName, String docTypeVersion, String dataFormat) {
        // First: try to find exact match
        for (SplitFlowConfig config : docTypeSplitConfigs) {
            if (config.getName().equalsIgnoreCase(docTypeName)
                    && config.getVersion().equalsIgnoreCase(docTypeVersion)
                    && (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
                return config;
            }
        }
        // Second: try to find by docType (ignore version)
        for (SplitFlowConfig config : docTypeSplitConfigs) {
            if (config.getName().equalsIgnoreCase(docTypeName)
                    && (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
                return config;
            }
        }
        // Fallback: return default if present
        //if ("default".equalsIgnoreCase(dataFormat)) return defaultConfig;
        return defaultConfig;
    }
    
    public boolean isGenericSplitting() {
		return genericSplitting;
	}
	public void setGenericSplitting(boolean genericSplitting) {
		this.genericSplitting = genericSplitting;
	}
	public String getSplitterRegex() {
		return splitterRegex;
	}
	public void setSplitterRegex(String splitterRegex) {
		this.splitterRegex = splitterRegex;
	}
}

/**
 * Custom deserializer to handle dot notation with array indices like:
 * "configurationList[0].documentName": "INVOICE"
 * "configurationList[0].documentVersion": "1.0"
 * "configurationList[0].dataFormat": "EDI_X12"
 *
 * Converts them to proper List<DocTypeConfig> structure.
 */
class SplitterFlowStepConfigDeserializer extends JsonDeserializer<SplitterFlowStepConfig> {

    @Override
    public SplitterFlowStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) parser.getCodec();
        JsonNode root = mapper.readTree(parser);

        SplitterFlowStepConfig result = new SplitterFlowStepConfig();

        Map<Integer, ObjectNode> configMap = new HashMap<>();
        ObjectNode defaultConfigNode = mapper.createObjectNode();

        Iterator<Map.Entry<String, JsonNode>> fields = root.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (key.startsWith("configurationList[")) {
                int indexStart = key.indexOf('[') + 1;
                int indexEnd = key.indexOf(']');
                int index = Integer.parseInt(key.substring(indexStart, indexEnd));
                String fieldName = key.substring(key.indexOf(']') + 2);

                ObjectNode node = configMap.computeIfAbsent(index, k -> mapper.createObjectNode());
                node.set(fieldName, value);

            } else if (key.startsWith("defaultConfig.")) {
                String fieldName = key.substring("defaultConfig.".length());
                defaultConfigNode.set(fieldName, value);

            } else if ("stepName".equals(key)) {
                result.setStepName(value.asText());
            } else if ("copyHeaders".equals(key)) {
                result.setCopyHeaders(value.asBoolean());
            } else if ("genericSplitting".equals(key)) {
                result.setGenericSplitting(value.asBoolean());
            } else if ("splitterRegex".equals(key)) {
                result.setSplitterRegex(value.asText());
            }
        }

        // Deserialize configurationList entries
        List<SplitFlowConfig> configList = new ArrayList<>();
        for (ObjectNode node : configMap.values()) {
            SplitFlowConfig config = mapper.treeToValue(node, SplitFlowConfig.class);
            configList.add(config);
        }
        result.setDocTypeSplitConfigs(configList);

        // Deserialize defaultConfig
        if (!defaultConfigNode.isEmpty()) {
            DefaultSplitterConfig defaultConfig = mapper.treeToValue(defaultConfigNode, DefaultSplitterConfig.class);
            result.setDefaultConfig(defaultConfig);
        }

        return result;
    }
}