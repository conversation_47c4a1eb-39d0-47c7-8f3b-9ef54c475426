package com.dell.it.hip.strategy.flows;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.SerializationUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AggregatorFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.DocTypeConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig.DocTypeStrictOrderConfig;
import com.dell.it.hip.core.registry.StrictOrderMetricRegistry;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;

@Component("strictOrderProcessor")
public class StrictOrderProcessorFlowStepStrategy extends AbstractFlowStepStrategy {

    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redissonClient;
    private final WiretapService wiretapService;
    private final StrictOrderMetricRegistry metricRegistry;
    private FlowStepConfigRef stepConfigRef;

    public StrictOrderProcessorFlowStepStrategy(
            StringRedisTemplate redisTemplate,
            RedissonClient redissonClient,
            WiretapService wiretapService,
            StrictOrderMetricRegistry metricRegistry
    ) {
        this.redisTemplate = redisTemplate;
        this.redissonClient = redissonClient;
        this.wiretapService = wiretapService;
        this.metricRegistry = metricRegistry;
    }

    @Override
    public String getType() {
        return "strictOrderProcessor";
    }

    @Override
    public List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) throws Exception {
        StrictOrderConfig config = (StrictOrderConfig) def.getConfig(stepConfigRef.getPropertyRef(), StrictOrderConfig.class);

        if (config == null) {
            emitWiretapError(message, def, stepConfigRef, "No Strict Order Processor config for: " + stepConfigRef.getPropertyRef());
            return Collections.emptyList();
        }
        
     // Default/fallback config logic
        String docTypeName = null;
        String docTypeVersion = null;
        this.stepConfigRef = stepConfigRef;
        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null)
            dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));

        // === Find matching docType config or default ===
        StrictOrderConfig.DocTypeStrictOrderConfig dtConfig = findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        StrictOrderConfig.DefaultStrictOrderConfig defaultCfg = config.getDefaultConfig();

        StrictOrderConfig.StrictOrderBehavior behavior = dtConfig != null ? dtConfig.getBehavior()
                           : (defaultCfg != null ? defaultCfg.getBehavior() : StrictOrderConfig.StrictOrderBehavior.STRICTORDER);
        
        // === Behavior handling ===
        if (behavior == StrictOrderConfig.StrictOrderBehavior.SKIP) {
            wiretapService.tap(message, def, stepConfigRef, "info", "Strict Order Processor: SKIP for docType " + docTypeHeader);
            return Collections.singletonList(message);
        }
        if (behavior == StrictOrderConfig.StrictOrderBehavior.TERMINATE) {
            wiretapService.tap(message, def, stepConfigRef, "terminated", "Strict Order Processor: TERMINATE for docType " + docTypeHeader);
            return Collections.emptyList();
        }
       
        // Build the partition key
        String partitionKey = buildPartitionKey(def, dtConfig, message);

        // Extract the sequence number (required for ordering)
        Long seq = getSequenceNumber(message, dtConfig);
        if (seq == null) {
            wiretapService.tap(message, def, stepConfigRef, "error", "StrictOrder: No sequence number in message for partition " + partitionKey);
            metricRegistry.incrementMissing(def, stepConfigRef.getPropertyRef());
            return List.of(); // Discard or send to error channel
        }

        String redisZsetKey = strictOrderZSetKey(def, dtConfig, partitionKey, stepConfigRef);
        String redisSeqKey = strictOrderSeqKey(def, dtConfig, partitionKey, stepConfigRef);

        RLock lock = redissonClient.getLock(redisZsetKey + ":lock");
        lock.lock(10, TimeUnit.SECONDS);
        try {
            // Get the expected sequence number for this partition
            String expectedSeqStr = redisTemplate.opsForValue().get(redisSeqKey);
            long expectedSeq = (expectedSeqStr == null) ? seq : Long.parseLong(expectedSeqStr);

            if (seq == expectedSeq) {
                // Release this message and any consecutive queued
                List<Message<?>> ready = new ArrayList<>();
                ready.add(message);

                // Fetch buffered messages and check for further consecutive sequences
                boolean more = true;
                long nextSeq = expectedSeq + 1;
                while (more) {
                    Set<String> popped = redisTemplate.opsForZSet().rangeByScore(redisZsetKey, nextSeq, nextSeq);
                    if (popped == null || popped.isEmpty()) break;
                    String bufferedMsg = popped.iterator().next();
                    Message<?> m = deserialize(bufferedMsg);
                    Long bufferedSeq = getSequenceNumber(m, dtConfig);
                    if (bufferedSeq != null && bufferedSeq == nextSeq) {
                        ready.add(m);
                        redisTemplate.opsForZSet().remove(redisZsetKey, bufferedMsg);
                        nextSeq++;
                    } else {
                        more = false;
                    }
                }
                // Update expected seq
                redisTemplate.opsForValue().set(redisSeqKey, String.valueOf(nextSeq));
                metricRegistry.incrementInOrder(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "info", "StrictOrder: Released " + ready.size() + " messages for partition " + partitionKey);
                return ready;
            } else if (seq > expectedSeq) {
                // Buffer out-of-order message
                redisTemplate.opsForZSet().add(redisZsetKey, serialize(message), seq);
                metricRegistry.incrementOutOfOrder(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "warn", "StrictOrder: Buffered out-of-order msg seq " + seq + " (expected " + expectedSeq + ") partition " + partitionKey);
                return List.of();
            } else {
                // Duplicate or too-late message
                metricRegistry.incrementMissing(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "warn", "StrictOrder: Late or duplicate message seq " + seq + " for partition " + partitionKey);
                return List.of();
            }
        } finally {
            lock.unlock();
        }
    }

    /** Manual release for ops; returns all released messages. */
    public List<Message<?>> manualRelease(HIPIntegrationDefinition def, StrictOrderConfig.DefaultStrictOrderConfig config, List<String> orderingKeyValues, Long uptoSeq) {
        String partitionKey = buildPartitionKey(def, orderingKeyValues);
        String redisZsetKey = strictOrderZSetKey(def, new DocTypeStrictOrderConfig(), partitionKey, stepConfigRef);

        RLock lock = redissonClient.getLock(redisZsetKey + ":lock");
        lock.lock(10, TimeUnit.SECONDS);
        try {
            Set<String> buffered = uptoSeq == null
                    ? redisTemplate.opsForZSet().range(redisZsetKey, 0, -1)
                    : redisTemplate.opsForZSet().rangeByScore(redisZsetKey, 0, uptoSeq);
            List<Message<?>> msgs = new ArrayList<>();
            if (buffered != null) {
                for (String raw : buffered) {
                    msgs.add(deserialize(raw));
                    redisTemplate.opsForZSet().remove(redisZsetKey, raw);
                }
            }
            // Optionally increment a manualRelease counter if you want
            return msgs;
        } finally {
            lock.unlock();
        }
    }

    // -- Key construction: Unique per ServiceManager, integration, version, flowstep, partition, docType
    private String strictOrderZSetKey(HIPIntegrationDefinition def, StrictOrderConfig.DocTypeStrictOrderConfig config, String partitionKey, FlowStepConfigRef stepConfigRef) {
        return "hip:strictorder:" +
                def.getServiceManagerName() + ":" +
                def.getHipIntegrationName() + ":" +
                def.getVersion() + ":" +
                stepConfigRef.getPropertyRef() + ":" +
                config.getDocumentTypeId() + ":" +
                partitionKey;
    }

    private String strictOrderSeqKey(HIPIntegrationDefinition def, StrictOrderConfig.DocTypeStrictOrderConfig config, String partitionKey, FlowStepConfigRef stepConfigRef) {
        return strictOrderZSetKey(def, config, partitionKey, stepConfigRef) + ":nextSeq";
    }

    // -- Partition Key
    private String buildPartitionKey(HIPIntegrationDefinition def, StrictOrderConfig.DocTypeStrictOrderConfig config, Message<?> msg) {
        StringBuilder sb = new StringBuilder();
        for (String header : config.getOrderingKeys()) {
            Object val = msg.getHeaders().get(header);
            sb.append(header).append("=").append(val != null ? val : "null").append("|");
        }
        return sb.toString();
    }
    // Overload for manual release
    private String buildPartitionKey(HIPIntegrationDefinition def, List<String> keyVals) {
        return String.join("|", keyVals);
    }

    // -- Extract seq number from header
    private Long getSequenceNumber(Message<?> message, StrictOrderConfig.DocTypeStrictOrderConfig config) {
        Object seqObj = message.getHeaders().get(config.getSequenceHeader());
        if (seqObj instanceof Number) return ((Number) seqObj).longValue();
        if (seqObj instanceof String) try { return Long.parseLong((String) seqObj); } catch (Exception ignored) {}
        return null;
    }

    // --- (De)Serialization of Messages (use your preferred mechanism) ---
    private String serialize(Message<?> msg) {
        // You can use Jackson, or Spring’s message converter, etc.
        // For demo, just use Java serialization or Base64, but in prod use JSON!
        return Base64.getEncoder().encodeToString(SerializationUtils.serialize(msg));
    }

    private Message<?> deserialize(String s) {
        try {
            return (Message<?>) SerializationUtils.deserialize(Base64.getDecoder().decode(s));
        } catch (Exception e) {
            return null;
        }
    }
    
    // === docType config matching ===
    private StrictOrderConfig.DocTypeStrictOrderConfig findMatchingDocTypeConfig(StrictOrderConfig config, String docTypeName, String docTypeVersion, String dataFormat ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (StrictOrderConfig.DocTypeStrictOrderConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }
    
    private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError", errorMsg);
    }

}