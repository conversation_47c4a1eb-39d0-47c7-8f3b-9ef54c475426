package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMapConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.MappingTransformerFlowStepConfig;
import com.dell.it.hip.core.registry.ContivoTransformerRegistry;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.contivoUtils.ContivoTransformerService;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;

@Component("mappingTrasformer")
public class MappingTransformerFlowStepStrategy extends AbstractFlowStepStrategy {
    private final RuleProcessor ruleProcessor;
    private final ContivoTransformerRegistry transformerRegistry;
    private ContivoTransformerService contivosrvice;

    public MappingTransformerFlowStepStrategy(
            RuleProcessor ruleProcessor,
            ContivoTransformerRegistry transformerRegistry
    ) {
        this.ruleProcessor = ruleProcessor;
        this.transformerRegistry = transformerRegistry;
    }

    public String getType() {
        return "mappingTrasformer";
    }

    public List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        MappingTransformerFlowStepConfig config = (MappingTransformerFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), MappingTransformerFlowStepConfig.class);
        if (config == null) {
            emitWiretapError(message, def, stepConfigRef, "No Mapping Transformer config for: " + stepConfigRef.getPropertyRef());
            return Collections.emptyList();
        }
        
        String docTypeName = null;
        String docTypeVersion = null;

        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }
        String dataFormat = (String) message.getHeaders().get("hip.source.dataFormat");
        if (dataFormat == null)
            dataFormat = MessageFormatDetector.detect(String.valueOf(message.getPayload()));

     // === Find matching docType config or default ===
        MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig dtConfig = findMatchingDocTypeConfig(config, docTypeName, docTypeVersion, dataFormat);
        MappingTransformerFlowStepConfig.DefaultMappingTransformerConfig defaultCfg = config.getDefaultConfig();

        MappingTransformerFlowStepConfig.MappingBehavior behavior = dtConfig != null ? dtConfig.getBehavior()
                           : (defaultCfg != null ? defaultCfg.getBehavior() : MappingTransformerFlowStepConfig.MappingBehavior.TRANSFORM);
        
     // === Behavior handling ===
        if (behavior == MappingTransformerFlowStepConfig.MappingBehavior.SKIP) {
            wiretapService.tap(message, def, stepConfigRef, "info", "Mapping Transformer: SKIP for docType " + docTypeHeader);
            return Collections.singletonList(message);
        }
        if (behavior == MappingTransformerFlowStepConfig.MappingBehavior.TERMINATE) {
            wiretapService.tap(message, def, stepConfigRef, "terminated", "Mapping Transformer: TERMINATE for docType " + docTypeHeader);
            return Collections.emptyList();
        }
        
  
        Map<String, Object> context = new HashMap<>();
        Message<?> messageWithMap = ruleProcessor.processRules(
                message,
                dtConfig.getRuleRefs(),
                def,
                stepConfigRef,
                context,
                dtConfig.isDbBacked()
        );

        ContivoMapConfig contivoMap = (ContivoMapConfig) context.get("contivoMap");
        if (contivoMap == null) {
            throw new RuntimeException("No ContivoMap found in context after rule processing.");
        }

        String transformedPayload = contivosrvice.transform(messageWithMap.getPayload().toString(), contivoMap);

        Message<?> outputMsg = MessageBuilder.withPayload(transformedPayload)
                .copyHeaders(messageWithMap.getHeaders())
                .setHeader("hip.contivoMapId", contivoMap.getMapIdentifier())
                .setHeader("hip.contivoMapVersion", contivoMap.getMapIdentifierVersion())
                .setHeader("hip.contivoMapName", contivoMap.getMapName())
                .build();

        return Collections.singletonList(outputMsg);
    }
    
 // === docType config matching ===
    private MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig findMatchingDocTypeConfig(MappingTransformerFlowStepConfig config, String docTypeName, String docTypeVersion, String dataFormat ) {
        if (config.getDocTypeConfigs() == null) return null;
        for (MappingTransformerFlowStepConfig.DocTypeMappingTransformerConfig dtConfig : config.getDocTypeConfigs()) {
            if (dtConfig.getName().equalsIgnoreCase(docTypeName)
                    && (dtConfig.getVersion() == null || dtConfig.getVersion().equalsIgnoreCase(docTypeVersion))
                    && (dtConfig.getDataFormat() == null || dtConfig.getDataFormat().equalsIgnoreCase(dataFormat))) {
                return dtConfig;
            }
        }
        return null;
    }
    
    private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError", errorMsg);
    }
}