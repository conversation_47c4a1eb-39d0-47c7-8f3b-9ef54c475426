package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.core.HIPIntegrationOrchestrationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.core.repository.HIPIntegrationDefinitionStore;
import com.dell.it.hip.exception.IntegrationDuplicateException;
import com.dell.it.hip.exception.IntegrationRegistrationException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for duplicate validation logic in HIPIntegrationManagementController.
 * Tests the new duplicate registration validation functionality.
 */
@ExtendWith(MockitoExtension.class)
public class HIPIntegrationManagementControllerDuplicateValidationTest {

    private static final String SERVICE_MANAGER_NAME = "test-service-manager";
    private static final String INTEGRATION_NAME = "test-integration";
    private static final String VERSION = "1.0";

    @Mock
    private HIPIntegrationOrchestrationService orchestrationService;

    @Mock
    private ServiceManager serviceManager;

    @Mock
    private HIPIntegrationRuntimeService hipIntegrationRuntimeService;

    @Mock
    private HIPIntegrationDefinitionStore hipIntegrationRegistry;

    @InjectMocks
    private HIPIntegrationManagementController controller;

    private HIPIntegrationRequest testRequest;

    @BeforeEach
    void setUp() {
        // Set the service manager name using reflection
        ReflectionTestUtils.setField(controller, "serviceManagerName", SERVICE_MANAGER_NAME);

        // Create test request
        testRequest = new HIPIntegrationRequest();
        testRequest.setHipIntegrationName(INTEGRATION_NAME);
        testRequest.setVersion(VERSION);
        testRequest.setBusinessFlowName("test-flow");
    }

    @Test
    void testRegisterIntegration_Success_NoDuplicate() throws Exception {
        // Arrange
        when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION))
                .thenReturn(false);
        doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        ResponseEntity<?> response = controller.register(testRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("HIPIntegration registered", response.getBody());
        
        // Verify interactions
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION);
        verify(orchestrationService).registerHIPIntegration(testRequest);
    }

    @Test
    void testRegisterIntegration_ThrowsDuplicateException_WhenIntegrationExists() throws Exception {
        // Arrange
        when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION))
                .thenReturn(true);

        // Act & Assert
        IntegrationDuplicateException exception = assertThrows(IntegrationDuplicateException.class, () -> {
            controller.register(testRequest);
        });

        // Verify exception details
        assertEquals(SERVICE_MANAGER_NAME, exception.getServiceManagerName());
        assertEquals(INTEGRATION_NAME, exception.getIntegrationName());
        assertEquals(VERSION, exception.getVersion());
        assertTrue(exception.getMessage().contains(SERVICE_MANAGER_NAME));
        assertTrue(exception.getMessage().contains(INTEGRATION_NAME));
        assertTrue(exception.getMessage().contains(VERSION));
        assertTrue(exception.getMessage().contains("already exists"));

        // Verify interactions
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION);
        verify(orchestrationService, never()).registerHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRegisterIntegration_DifferentVersions_AllowsRegistration() throws Exception {
        // Arrange - Same integration name but different version
        testRequest.setVersion("2.0");
        when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, "2.0"))
                .thenReturn(false);
        doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        ResponseEntity<?> response = controller.register(testRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("HIPIntegration registered", response.getBody());
        
        // Verify interactions
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, "2.0");
        verify(orchestrationService).registerHIPIntegration(testRequest);
    }

    @Test
    void testRegisterIntegration_DifferentIntegrationNames_AllowsRegistration() throws Exception {
        // Arrange - Different integration name
        testRequest.setHipIntegrationName("different-integration");
        when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, "different-integration", VERSION))
                .thenReturn(false);
        doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act
        ResponseEntity<?> response = controller.register(testRequest);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals("HIPIntegration registered", response.getBody());
        
        // Verify interactions
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, "different-integration", VERSION);
        verify(orchestrationService).registerHIPIntegration(testRequest);
    }

    @Test
    void testRegisterIntegration_RepositoryException_PropagatesException() throws Exception {
        // Arrange
        RuntimeException repositoryException = new RuntimeException("Database connection failed");
        when(hipIntegrationRegistry.exists(anyString(), anyString(), anyString()))
                .thenThrow(repositoryException);

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            controller.register(testRequest);
        });

        assertEquals("Database connection failed", exception.getMessage());
        
        // Verify interactions
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION);
        verify(orchestrationService, never()).registerHIPIntegration(any(HIPIntegrationRequest.class));
    }

    @Test
    void testRegisterIntegration_OrchestrationServiceException_AfterDuplicateCheck() throws Exception {
        // Arrange
        when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION))
                .thenReturn(false);
        IntegrationRegistrationException orchestrationException = 
                new IntegrationRegistrationException(INTEGRATION_NAME, VERSION, "Configuration invalid");
        doThrow(orchestrationException).when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

        // Act & Assert
        IntegrationRegistrationException exception = assertThrows(IntegrationRegistrationException.class, () -> {
            controller.register(testRequest);
        });

        assertEquals(INTEGRATION_NAME, exception.getIntegrationName());
        assertEquals(VERSION, exception.getVersion());
        
        // Verify interactions - duplicate check should happen first
        verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, VERSION);
        verify(orchestrationService).registerHIPIntegration(testRequest);
    }

    @Test
    void testRegisterIntegration_VersionFormatVariations() throws Exception {
        // Test different version formats
        String[] versionFormats = {"1", "1.0", "1.0.0", "2.1.3", "10.5"};
        
        for (String version : versionFormats) {
            // Reset mocks for each iteration
            reset(hipIntegrationRegistry, orchestrationService);
            
            // Arrange
            testRequest.setVersion(version);
            when(hipIntegrationRegistry.exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, version))
                    .thenReturn(false);
            doNothing().when(orchestrationService).registerHIPIntegration(any(HIPIntegrationRequest.class));

            // Act
            ResponseEntity<?> response = controller.register(testRequest);

            // Assert
            assertEquals(200, response.getStatusCode().value());
            assertEquals("HIPIntegration registered", response.getBody());
            
            // Verify interactions
            verify(hipIntegrationRegistry).exists(SERVICE_MANAGER_NAME, INTEGRATION_NAME, version);
            verify(orchestrationService).registerHIPIntegration(testRequest);
        }
    }

    @Test
    void testRegisterIntegration_NullValues_HandledGracefully() throws Exception {
        // Arrange - Test with null integration name (should be caught by validation)
        testRequest.setHipIntegrationName(null);
        when(hipIntegrationRegistry.exists(anyString(), any(), anyString()))
                .thenReturn(false);

        // Act & Assert - This should not throw NPE but may throw validation exception
        assertDoesNotThrow(() -> {
            try {
                controller.register(testRequest);
            } catch (Exception e) {
                // Expected - validation or other business logic exception, but not NPE
                assertFalse(e instanceof NullPointerException, 
                    "Should not throw NullPointerException, got: " + e.getClass().getSimpleName());
            }
        });
    }
}
