package com.dell.it.hip.integration;

import java.time.Duration;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.condition.EnabledIf;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.DockerClientFactory;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

/**
 * Base class for integration tests using TestContainers.
 * Provides shared container setup and Docker environment validation.
 */
@SpringBootTest
@ActiveProfiles("test")
@Import(TestRedisConfiguration.class)
public abstract class BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(BaseIntegrationTest.class);

    // Shared containers for all integration tests - manually managed to avoid Docker initialization issues
    protected static PostgreSQLContainer<?> postgres;
    protected static GenericContainer<?> redis;
    protected static KafkaContainer kafka;

    private static boolean containersInitialized = false;

    /**
     * Validates Docker environment and initializes containers before running tests.
     */
    @BeforeAll
    static void checkDockerEnvironment() {
        if (!isDockerAvailable()) {
            logger.warn("Docker environment check failed - tests should be skipped");
            return;
        }

        logger.info("Docker environment validated successfully");

        // Initialize containers if not already done
        if (!containersInitialized) {
            initializeContainers();
            containersInitialized = true;
        }
    }

    /**
     * Initializes TestContainers when Docker is available.
     */
    private static void initializeContainers() {
        logger.info("Initializing TestContainers...");

        postgres = new PostgreSQLContainer<>(DockerImageName.parse("postgres:15-alpine"))
                .withDatabaseName("hip_test")
                .withUsername("test")
                .withPassword("test")
                .withStartupTimeout(Duration.ofMinutes(2))
                .withReuse(true);

        redis = new GenericContainer<>(DockerImageName.parse("redis:6.2.6"))
                .withExposedPorts(6379)
                .withStartupTimeout(Duration.ofMinutes(2))
                .withReuse(true);

        kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
                .withStartupTimeout(Duration.ofMinutes(2))
                .withReuse(true);

        // Start containers
        postgres.start();
        redis.start();
        kafka.start();

        logger.info("TestContainers initialized and started successfully");
    }

    /**
     * Configures Spring properties with TestContainer connection details.
     * Only configures properties if Docker is available and containers are initialized.
     */
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        if (!isDockerAvailable()) {
            logger.warn("Docker not available - disabling Redis and using embedded alternatives for testing");
            // Configure embedded alternatives or skip configuration
            registry.add("spring.datasource.url", () -> "jdbc:h2:mem:testdb");
            registry.add("spring.datasource.driver-class-name", () -> "org.h2.Driver");
            registry.add("spring.datasource.username", () -> "sa");
            registry.add("spring.datasource.password", () -> "");

            // DISABLE Redis completely when Docker is not available
            registry.add("spring.data.redis.enabled", () -> "false");

            // Use embedded Kafka alternative or mock
            registry.add("spring.kafka.bootstrap-servers", () -> "localhost:9092");

            // Disable Spring Cloud Config for tests
            registry.add("spring.cloud.config.enabled", () -> "false");
            registry.add("spring.cloud.config.import-check.enabled", () -> "false");

            // Provide required configuration properties with default values for tests
            registry.add("configserver_uri", () -> "http://localhost:8888");
            registry.add("configproperties_sheet_name", () -> "test-config");
            registry.add("SPRING_PROFILES_ACTIVE", () -> "test");

            logger.info("Embedded/mock configuration applied for testing without Docker - Redis DISABLED, Config Server DISABLED");
            return;
        }

        // PostgreSQL configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");

        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);
        registry.add("spring.data.redis.url", () -> "redis://" + redis.getHost() + ":" + redis.getFirstMappedPort());

        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);

        logger.info("TestContainers configuration applied:");
        logger.info("PostgreSQL: {}", postgres.getJdbcUrl());
        logger.info("Redis: {}:{}", redis.getHost(), redis.getFirstMappedPort());
        logger.info("Kafka: {}", kafka.getBootstrapServers());
    }

    /**
     * Checks if Docker is available for tests.
     * This method is used by @DisabledIf annotation to conditionally enable tests.
     * @return true if Docker is available, false otherwise
     */
    public static boolean isDockerAvailable() {
        try {
            boolean isDockerAvailable = DockerClientFactory.instance().isDockerAvailable();
            return isDockerAvailable;
        } catch (Exception e) {
            logger.debug("Docker availability check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Checks if Docker is NOT available for tests.
     * This method is used by @DisabledIf annotation to conditionally disable tests.
     * @return true if Docker is NOT available, false otherwise
     */
    public static boolean isDockerNotAvailable() {
        return !isDockerAvailable();
    }

    /**
     * Gets the PostgreSQL container for test-specific operations.
     */
    protected static PostgreSQLContainer<?> getPostgresContainer() {
        return postgres;
    }

    /**
     * Gets the Redis container for test-specific operations.
     */
    protected static GenericContainer<?> getRedisContainer() {
        return redis;
    }

    /**
     * Gets the Kafka container for test-specific operations.
     */
    protected static KafkaContainer getKafkaContainer() {
        return kafka;
    }
}
